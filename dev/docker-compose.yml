services:
  safe-content-ai:
    ports:
      - 8000:8000
    image: steelcityamir/safe-content-ai:latest

  dragonfly:
    image: 'docker.dragonflydb.io/dragonflydb/dragonfly'
    ulimits:
      memlock: -1
    # ports:
    #   - "6379:6379"
    # For better performance, consider `host` mode instead `port` to avoid docker NAT.
    # `host` mode is NOT currently supported in Swarm Mode.
    # https://docs.docker.com/compose/compose-file/compose-file-v3/#network_mode
    network_mode: "host"
    volumes:
      - dragonflydata:/data

  # prometheus:
  #   image: prom/prometheus:latest
  #   volumes:
  #     - ../../prometheus.yml:/etc/prometheus/prometheus.yml:ro  # Updated path to point to file in parent directory
  #   command:
  #     - '--config.file=/etc/prometheus/prometheus.yml'
  #     - '--web.enable-lifecycle'
  #   ports:
  #     - "9090:9090"
  #   restart: unless-stopped
  #   extra_hosts:
  #     - "host.docker.internal:host-gateway"

volumes:
  dragonflydata: