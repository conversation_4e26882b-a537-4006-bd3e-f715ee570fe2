# top-most EditorConfig file
root = true

# Default settings
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 4

# Python files
[*.py]
indent_size = 4
max_line_length = 100  # to match our Black formatter config
insert_final_newline = true

# JSON, YAML, TOML etc
[*.{json,yml,yaml,toml}]
indent_style = space
indent_size = 2

[*.md]
trim_trailing_whitespace = false  # avoid breaking intentional spacing

[*.sh]
indent_style = space
indent_size = 2

[*.txt]
indent_style = space
indent_size = 2
