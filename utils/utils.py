import discord
from discord.ext import commands


from sqlalchemy.dialects.postgresql import insert as pg_insert

import re
import json
import os
from datetime import datetime, timedelta
from sqlalchemy import case, select, desc
from sqlalchemy.orm import selectinload
from typing import TYPE_CHECKING, Optional, Union

from utils.modules.core.db.models import User, UserAchievement
from utils.modules.core.checks import is_interchat_staff

from data.badges import badges

if TYPE_CHECKING:
    from main import Bot
    from utils.constants import InterchatConstants
    from sqlalchemy.ext.asyncio import AsyncSession

discordInteraction = Union[commands.Context, discord.Interaction]


def get_source_user(source: discordInteraction):
    return source.user if isinstance(source, discord.Interaction) else source.author


def get_source_bot(source: discordInteraction):
    return source.client if isinstance(source, discord.Interaction) else source.bot


async def chunk_guilds(guilds):
    for guild in sorted(guilds, key=lambda g: g.member_count, reverse=True):
        if guild.chunked is False:
            await guild.chunk(cache=True)


def parse_discord_emoji(emoji_input: discord.Emoji | str | None) -> str:
    if not emoji_input:
        return '❓'

    if isinstance(emoji_input, discord.Emoji):
        emoji_id = emoji_input.id
        name = emoji_input.name
        animated = getattr(emoji_input, 'animated', False)
        extension = 'gif' if animated else 'png'
        emoji_url = f'https://cdn.discordapp.com/emojis/{emoji_id}.{extension}'
        return f'<img src="{emoji_url}" alt="{name}" class="custom-emoji">'

    if isinstance(emoji_input, str):
        custom_emoji_pattern = r'<(a?):([^:]+):(\d+)>'
        match = re.match(custom_emoji_pattern, emoji_input)
        if match:
            animated, name, emoji_id = match.groups()
            extension = 'gif' if animated else 'png'
            emoji_url = f'https://cdn.discordapp.com/emojis/{emoji_id}.{extension}'
            return f'<img src="{emoji_url}" alt="{name}" class="custom-emoji">'

        return emoji_input

    return '❓'


async def fetch_badges(
    bot: 'Bot',
    interaction: discord.Interaction['Bot'],
    constants: 'InterchatConstants',
    user: discord.User,
):
    badges_list: list[dict[str, str]] = []

    if user.id in constants.auth_users():
        badges_list.append(
            {
                'icon': parse_discord_emoji(bot.emotes.developer_badge),
                'title': 'InterChat Developer',
                'description': 'Core developer of InterChat',
            }
        )

    if is_interchat_staff(interaction, user=user):
        badges_list.append(
            {
                'icon': parse_discord_emoji(bot.emotes.staff_badge),
                'title': 'InterChat Staff',
                'description': 'InterChat staff member',
            }
        )

    async with bot.db.get_session() as session:
        stmt = select(User.badges).where(User.id == str(user.id))
        res = (await session.execute(stmt)).scalar()

    if res:
        for badge in res:
            badge_info = badges[badge]
            icon_attr = getattr(bot.emotes, badge_info['icon'], None)
            if icon_attr:
                data = {
                    'icon': parse_discord_emoji(icon_attr),
                    'title': badge_info['name'],
                    'description': badge_info['description'],
                }
                badges_list.append(data)

    return badges_list


async def fetch_achievements(
    bot: 'Bot',
    interaction: discord.Interaction['Bot'] | commands.Context['Bot'],
    user: discord.User,
    limit: int = 6,
):
    achievement_list: list[dict[str, str]] = []

    async with bot.db.get_session() as session:
        stmt = (
            select(UserAchievement)
            .where(UserAchievement.userId == str(user.id))
            .options(selectinload(UserAchievement.achievement))
            .order_by(desc(UserAchievement.unlockedAt))
            .limit(limit)
        )
        result = (await session.execute(stmt)).scalars().all()

        for ua in result:
            achievement = ua.achievement
            if not achievement:
                continue

            data = {
                'icon': parse_discord_emoji(achievement.badgeEmoji),
                'title': achievement.name,
                'description': achievement.description,
            }
            achievement_list.append(data)

    return achievement_list


async def load_profile_data(bot: 'Bot', user: discord.User) -> Optional['User']:
    async with bot.db.get_session() as session:
        stmt = select(User).where(User.id == str(user.id))
        res = (await session.execute(stmt)).scalar_one_or_none()

    return res


async def load_user_locale(bot: 'Bot', source: discordInteraction) -> str:
    user = get_source_user(source)
    async with bot.db.get_session() as session:
        stmt = select(User.locale).where(User.id == str(user.id))
        res = (await session.execute(stmt)).scalar()
    return res or 'en'


def check_user(ephemeral: bool = False):
    async def predicate(ctx: commands.Context['Bot']) -> bool:
        # FIXME: This is an emergency fix
        # await ctx.defer(ephemeral=ephemeral)
        async with ctx.bot.db.get_session() as session:
            stmt = select(User.id).where(User.id == str(ctx.author.id))
            existing = (await session.execute(stmt)).scalar()

            if not existing:
                user = User(
                    id=str(ctx.author.id),
                    name=ctx.author.name,
                    image=ctx.author.display_avatar.url,
                    locale='en',
                    badges=[],
                    preferredLanguages=[],
                    lastMessageAt=datetime.now(),
                    inboxLastReadDate=datetime.now(),
                    createdAt=datetime.now(),
                    updatedAt=datetime.now(),
                )
                session.add(user)
                await session.commit()

        return True

    return commands.check(predicate)


def abbreviate_number(n):
    if n >= 1_000_000_000:
        return f'{n / 1_000_000_000:.1f}B'
    elif n >= 1_000_000:
        return f'{n / 1_000_000:.1f}M'
    elif n >= 1_000:
        return f'{n / 1_000:.1f}k'
    else:
        return str(n)


def parse_duration(duration_str: str | None) -> Optional[int]:
    if not duration_str or not isinstance(duration_str, str):
        return None

    cleaned = duration_str.strip().lower()

    if not cleaned:
        return None

    time_units = {
        'm': 60000,
        'min': 60000,
        'mins': 60000,
        'minutes': 60000,
        'h': 3600000,
        'hr': 3600000,
        'hrs': 3600000,
        'hours': 3600000,
        'd': 86400000,
        'day': 86400000,
        'days': 86400000,
        'ds': 86400000,
        'w': 604800000,
        'week': 604800000,
        'weeks': 604800000,
        'ws': 604800000,
        'wk': 604800000,
        'wks': 604800000,
        'M': 2592000000,
        'month': 2592000000,
        'months': 2592000000,
        'ms': 2592000000,
        'mo': 2592000000,
        'mos': 2592000000,
        'y': 31536000000,
        'year': 31536000000,
        'years': 31536000000,
        'ys': 31536000000,
        'yr': 31536000000,
        'yrs': 31536000000,
    }

    pattern = r'^(\d+)\s*([a-zA-Z]+)$|^(\d+)\s*([a-zA-Z]+)\s*([a-zA-Z]+)$'

    match = re.match(pattern, cleaned)
    if not match:
        return None

    number = int(match.group(1)) if match.group(1) else int(match.group(2))
    unit = match.group(2).lower() if match.group(2) else match.group(3).lower()

    if unit in time_units:
        return number * time_units[unit]

    return None


def duration_to_datetime(duration_ms: Optional[int]) -> Optional[datetime]:
    if duration_ms is None:
        return None
    return datetime.now() + timedelta(milliseconds=duration_ms)


async def upsert_user(user: discord.User | discord.Member, session: 'AsyncSession') -> User:
    """Upsert user with efficient database operations"""
    user_id = str(user.id)
    current_name = user.name
    current_avatar = user.display_avatar.url

    # Use PostgreSQL UPSERT instead of SQLAlchemy
    stmt = pg_insert(User).values(
        id=user_id,
        name=current_name,
        image=current_avatar,
        lastMessageAt=datetime.now(),
        updatedAt=datetime.now(),
    )

    # On conflict: Always update timestamps, conditionally update name/image
    stmt = stmt.on_conflict_do_update(
        index_elements=['id'],
        set_={
            'name': case((User.name != stmt.excluded.name, stmt.excluded.name), else_=User.name),
            'image': case(
                (User.image != stmt.excluded.image, stmt.excluded.image),
                else_=User.image,
            ),
            'lastMessageAt': stmt.excluded.lastMessageAt,
            'updatedAt': stmt.excluded.updatedAt,
        },
    ).returning(User)

    return (await session.execute(stmt)).scalar_one()


async def load_locales():
    with open('./data/localeMap.json', 'r', encoding='utf-8') as f:
        locale_map = json.load(f)

    abbrev_to_name = {v['code']: k for k, v in locale_map.items()}

    available_locales = []

    for root, _, files in os.walk('./locales'):
        for file in files:
            if file.endswith('.yaml'):
                lang_code = file.removesuffix('.yaml')
                if lang_code in abbrev_to_name:
                    full_name = abbrev_to_name[lang_code]

                    flag = locale_map[full_name].get('flag', '🌐')

                    available_locales.append({'short': lang_code, 'long': full_name, 'flag': flag})

    available_locales.sort(key=lambda x: x['long'])
    return available_locales
