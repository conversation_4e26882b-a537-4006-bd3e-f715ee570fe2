import os
import ast
from dotenv import load_dotenv
import colorlog
import logging
import redis.asyncio as redis_async

load_dotenv()


class InterchatConstants:
    def environment(self) -> str:
        return str(os.getenv('ENVIRONMENT'))

    def debug(self) -> bool:
        return bool(os.getenv('DEBUG'))

    def version(self) -> str:
        version = str(os.getenv('INTERCHAT_VERSION'))
        if str(os.getenv('ENVIRONMENT')).lower() == 'development':
            return f'DEV {version}'

        return version

    def token(self) -> str:
        return str(os.getenv('TOKEN'))

    def database_url(self) -> str:
        return str(os.getenv('DATABASE_URL'))

    def redis_uri(self) -> str:
        return str(os.getenv('REDIS_URI'))

    def prefix(self) -> str:
        return str(os.getenv('PREFIX'))

    def color(self) -> int:
        return 0x9172D8

    def support_invite(self) -> str:
        return str(os.getenv('SUPPORT_INVITE'))

    def donate_link(self) -> str:
        return str(os.getenv('DONATE_LINK'))

    def sentry_dsn(self) -> str:
        return str(os.getenv('SENTRY_DSN'))

    def rate_limits(self) -> dict[str, dict[str, int]]:
        return {'commands': {'limit': 5, 'period': 5}, 'webhook': {'limit': 3, 'period': 300}}

    def auth_users(self) -> list[int]:
        return ast.literal_eval(os.getenv('AUTH'))

    def dev_guild_id(self) -> int:
        return int(os.getenv('DEV_GUILD_ID'))

    def staff_role_id(self) -> int:
        return int(os.getenv('STAFF_ROLE_ID'))


constants = InterchatConstants()

redis_client = redis_async.from_url(
    constants.redis_uri(),
    max_connections=100,
    socket_connect_timeout=10,
    socket_timeout=10,
    retry_on_timeout=True,
    decode_responses=True,
)

log = colorlog.ColoredFormatter(
    '%(blue)s[%(asctime)s]%(reset)s - %(filename)s - %(log_color)s%(levelname)s%(reset)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    log_colors={
        'DEBUG': 'cyan',
        'INFO': 'green',
        'WARNING': 'yellow',
        'ERROR': 'red',
        'CRITICAL': 'bold_red',
    },
)

handler = logging.StreamHandler()
handler.setFormatter(log)

logger = logging.getLogger('InterChat')
logger.addHandler(handler)
if constants.debug():
    logger.setLevel(logging.DEBUG)
else:
    logger.setLevel(logging.INFO)
