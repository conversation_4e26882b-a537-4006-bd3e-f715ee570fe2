import discord
from discord.ui import View, button, Button

from sqlalchemy import select

from utils.modules.ui.CreateEmbed import get_text
from utils.modules.core.db.models import User, Hub


class RuleButtons(View):
    def __init__(self, bot, user):
        super().__init__(timeout=300)
        self.bot = bot
        self.user = user

        self.setup_buttons()

    def setup_buttons(self):
        self.accept_callback.emoji = self.bot.emotes.tick_icon
        self.decline_callback.emoji = self.bot.emotes.x_icon

    @button(emoji='❓', label='Accept', style=discord.ButtonStyle.green)
    async def accept_callback(self, interaction: discord.Interaction, button: Button): ...

    @button(emoji='❓', label='Decline', style=discord.ButtonStyle.grey)
    async def decline_callback(self, interaction: discord.Interaction, button: Button): ...


async def has_accepted(interaction):
    async with interaction.bot.db.get_session() as session:
        stmt = select(Hub).where(Hub.rulesAcceptances.any(User.id == str(interaction.user.id)))
        result = (await session.execute(stmt)).scalars().all()

    if not result:
        return False

    return True


async def send_rules(interaction, hub):  # CHANGE ME TO INTERACTION
    await interaction.response.defer()

    async with interaction.bot.db.get_session() as session:
        stmt = select(Hub.rules).where(Hub.name == hub)
        rules = (await session.execute(stmt)).scalars().all()

    if rules == []:
        rules = get_text(
            'en', 'rules.rules', guidelines_link='https://www.interchat.tech/guidelines'
        )

    user = interaction.user
    embed = discord.Embed(
        title=f'{hub.name} Rules', description=rules, color=0x9172D8
    )  # change color to fit constants
    embed.set_author(name=f'@{user.name}', icon_url=user.display_avatar.url)
    embed.set_footer(text=f'Hub: {hub.name}')
    view = RuleButtons(interaction.bot, user)

    await interaction.response.followup.send(embed=embed, view=view, ephemeral=True)
