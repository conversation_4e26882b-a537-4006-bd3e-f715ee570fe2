from utils.modules.core.db.models import Hub, Role
from utils.modules.hub.constants import HubPermissionLevel


def get_user_permission(hub: Hub, user_id: str) -> HubPermissionLevel:
    if hub.ownerId == user_id:
        return HubPermissionLevel.OWNER

    for mod in hub.moderators:
        if mod.userId != user_id:
            continue
        if mod.role == Role.MANAGER:
            return HubPermissionLevel.MANAGER
        elif mod.role == Role.MODERATOR:
            return HubPermissionLevel.MODERATOR

    return HubPermissionLevel.NONE
