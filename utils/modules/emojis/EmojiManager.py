import discord

from utils.modules.emojis.emojisStatic import Emojis as EmojiHints


class EmojiManager(EmojiHints):
    def __init__(self):
        self._emojis: dict[str, discord.Emoji] = {}

    async def load(self, bot: discord.Client):
        emojis = await bot.fetch_application_emojis()
        self._emojis = {e.name: e for e in emojis}
        self.__dict__.update(self._emojis)

    def __getattr__(self, name: str):
        if name in self._emojis:
            return self._emojis[name]
        raise AttributeError(f"Emoji '{name}' not found.")
