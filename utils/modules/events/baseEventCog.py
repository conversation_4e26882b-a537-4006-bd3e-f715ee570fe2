from discord.ext import commands
from utils.modules.events.eventDispatcher import event_dispatcher
from typing import Callable, Awaitable
import inspect

EventCallback = Callable[..., Awaitable[None]]


class BaseEventCog(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self._register_all_event_listeners()

    async def cog_unload(self):
        self._unregister_all_event_listeners()

    def _register_all_event_listeners(self):
        for attr_name in dir(self):
            attr = getattr(self, attr_name)
            if callable(attr) and hasattr(attr, '_hub_event_listener'):
                if not inspect.iscoroutinefunction(attr):
                    raise TypeError(f'Event handler {attr_name} must be async')
                event_dispatcher.listen(attr._hub_event_listener, attr)

    def _unregister_all_event_listeners(self):
        for attr_name in dir(self):
            attr = getattr(self, attr_name)
            if callable(attr) and hasattr(attr, '_hub_event_listener'):
                if not inspect.iscoroutinefunction(attr):
                    raise TypeError(f'Event handler {attr_name} must be async')
                event_dispatcher.unlisten(attr._hub_event_listener, attr)
