from datetime import datetime
import re
from typing import TYPE_CHECKING, Optional, Tuple, List, Dict, Any
import discord
from urllib.parse import urlparse

from sqlalchemy import literal, select, Index, text
from sqlalchemy.orm import selectinload
from sqlalchemy.dialects.postgresql import insert

from utils.modules.core.db.models import Broadcast, Hub, Message, User
from data.badges import badges

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession
    from main import Bot


# Compile regex patterns once at module level for better performance
URL_PATTERN = re.compile(r'https?://[^\s]+')
IMAGE_URL_PATTERN = re.compile(r'\[⁥\]\(([^)]+)\)')

# Precompile common URL checks for performance
IMAGE_EXTENSIONS = frozenset(['.png', '.webp', '.jpg', '.jpeg'])
GIF_EXTENSION = '.gif'
TENOR_DOMAIN = 'tenor.com'

# Cache for frequently accessed data
_badge_cache: Dict[str, str] = {}
_user_badge_cache: Dict[str, Tuple[bool, List[str]]] = {}
CACHE_TTL = 300  # 5 minutes


def is_image_url(url: str) -> bool:
    """Check if URL is an image (png, webp, jpg, jpeg)"""
    try:
        parsed_url = urlparse(url.lower())
        return any(parsed_url.path.endswith(ext) for ext in IMAGE_EXTENSIONS)
    except (ValueError, AttributeError):
        return False


def is_gif_url(url: str) -> bool:
    """Check if URL is a GIF"""
    try:
        parsed_url = urlparse(url.lower())
        return parsed_url.path.endswith(GIF_EXTENSION)
    except (ValueError, AttributeError):
        return False


def is_tenor_gif(url: str) -> bool:
    """Check if URL is from Tenor"""
    return TENOR_DOMAIN in url.lower()


def format_message_with_attachments(
    content: str,
    attachments: List[discord.Attachment],
    stickers: List[discord.StickerItem],
) -> str:
    """Process message content and add attachment/sticker URLs"""
    if not content:
        content = ""
    
    _content = content

    # Process attachments
    for attachment in attachments:
        if is_image_url(attachment.url) or is_gif_url(attachment.url):
            # Add image/GIF with special format
            _content += f'\n[⁥]({attachment.url})'

    # Process stickers
    for sticker in stickers:
        _content += f'\n[⁥]({sticker.url})'

    # Check for image/GIF URLs in content - use precompiled regex
    urls = URL_PATTERN.findall(_content)

    for url in urls:
        try:
            if is_gif_url(url) and not is_tenor_gif(url):
                # Remove non-Tenor GIF URLs from content
                _content = _content.replace(url, '[GIF blocked - only Tenor GIFs allowed]')
            else:
                # Remove other URLs from content
                _content = _content.replace(url, '` URL removed `')
        except Exception:
            # Skip malformed URLs
            continue

    return _content


async def get_original_message_author(
    replied_message_id: str, session: 'AsyncSession'
) -> Optional[Tuple[Message, User, str, str]]:
    try:
        stmt = select(Message, User, Message.guildId, literal("message").label("src")) \
            .join(User, Message.authorId == User.id) \
            .where(Message.id == replied_message_id) \
            .union_all(
                select(Message, User, Message.guildId, literal("broadcast")) \
                .join(Broadcast, Broadcast.messageId == Message.id) \
                .join(User, Message.authorId == User.id) \
                .where(Broadcast.id == replied_message_id)
            )
        
        result = await session.execute(stmt)
        first_result = result.first()

        return first_result.tuple() if first_result else None
    except Exception as e:
        print(f"Error in get_original_message_author: {e}")
        return None


async def format_user_badges(bot: 'Bot', user_id: str, session: 'AsyncSession') -> str:
    """
    Format user badges with caching for performance.
    """
    try:
        # Validate user_id format
        if not user_id or not user_id.isdigit():
            return ''

        # Check cache first (implement your own cache invalidation strategy)
        cache_key = f"badges_{user_id}"
        if cache_key in _user_badge_cache:
            show_badges, user_badges = _user_badge_cache[cache_key]
            if not show_badges or not user_badges:
                return ''
        else:
            # Query database only if not cached
            stmt = select(User.showBadges, User.badges).where(User.id == user_id)
            result = await session.execute(stmt)
            user_data = result.first()

            if not user_data or not user_data.showBadges:
                _user_badge_cache[cache_key] = (False, [])
                return ''
            
            if not user_data.badges:
                _user_badge_cache[cache_key] = (True, [])
                return ''
            
            # Cache the result
            _user_badge_cache[cache_key] = (user_data.showBadges, user_data.badges)
            show_badges, user_badges = user_data.showBadges, user_data.badges

        badge_emojis = []
        for badge in user_badges:
            if badge in badges:
                badge_info = badges[badge]
                icon_attr = getattr(bot.emotes, badge_info.get('icon', ''), None)
                if icon_attr:
                    badge_emojis.append(str(icon_attr))

        if badge_emojis:
            return f"-# {' '.join(badge_emojis)}\n"

        return ''
        
    except Exception as e:
        print(f"Error in format_user_badges: {e}")
        return ''


def create_allowed_mentions_for_broadcast(
    replied_user_id: Optional[str], target_server_id: str, original_server_id: Optional[str]
) -> discord.AllowedMentions:
    """
    Create AllowedMentions object for broadcasts.
    Only allows mentions for replies going to the original server.
    """
    # Block all mentions by default
    allowed_mentions = discord.AllowedMentions.none()
    
    # Only allow user mention if this is a reply going to the original server
    if (replied_user_id and original_server_id and 
        target_server_id == original_server_id):
        try:
            # Validate user ID before creating discord object
            user_id_int = int(replied_user_id)
            allowed_mentions.users = [discord.Object(id=user_id_int)]
        except (ValueError, TypeError):
            # Invalid user ID, keep mentions blocked
            pass
    
    return allowed_mentions


async def create_reply_embed(
    message_reference: discord.MessageReference | None, session: 'AsyncSession'
) -> Optional[discord.Embed]:
    """Create reply embed if message is a reply"""
    if not message_reference or not message_reference.message_id:
        return None

    try:
        replied_message_id = str(message_reference.message_id)
        original_data = await get_original_message_author(replied_message_id, session)

        if original_data:
            original_message, author, _, _ = original_data
            return create_embed_from_message_and_author(original_message, author)

        return None
    except Exception as e:
        print(f"Error in create_reply_embed: {e}")
        return None


def create_embed_from_message_and_author(message: "Message", author: "User") -> discord.Embed:
    """Helper method to create embed from message and author"""
    try:
        # Safely handle None values and truncate username to 30 characters
        username = (author.name or 'Unknown User')[:30]

        # Safely handle None content and truncate to 100 characters
        content = message.content or ''
        if len(content) > 100:
            content = content[:100] + '...'

        embed = discord.Embed(
            description=content,
            color=discord.Colour.random(),
        )
        
        author_image = getattr(author, 'image', None)

        if author_image:
            embed.set_author(name=username, icon_url=author_image)
        
        return embed
    except Exception as e:
        print(f"Error in create_embed_from_message_and_author: {e}")
        return discord.Embed(
            description="Error loading reply preview",
            color=discord.Colour.red()
        )

async def store_message_and_broadcasts(
    message: discord.Message,
    hub: Hub,
    processed_content: str,
    broadcast_message_ids: List[Tuple[str, str]],
    session: 'AsyncSession',
) -> None:
    """
    CRITICAL OPTIMIZATION: Use PostgreSQL's ON CONFLICT for upserts
    and bulk operations for maximum performance at scale.
    """
    try:
        # Extract image URL from processed content if present
        image_url = None
        if '[⁥](' in processed_content:
            match = IMAGE_URL_PATTERN.search(processed_content)
            if match:
                image_url = match.group(1)

        # Safely handle guild ID
        guild_id = str(message.guild.id) if message.guild else ''
        
        # Safely handle message reference
        referred_message_id = None
        if message.reference and message.reference.message_id:
            referred_message_id = str(message.reference.message_id)

        # Use raw SQL for maximum performance on bulk inserts
        current_time = datetime.now()
        
        # Insert original message with ON CONFLICT handling
        message_insert = text("""
            INSERT INTO messages (id, hub_id, content, image_url, channel_id, 
                                guild_id, author_id, created_at, referred_message_id, reactions)
            VALUES (:id, :hub_id, :content, :image_url, :channel_id, 
                   :guild_id, :author_id, :created_at, :referred_message_id, :reactions)
            ON CONFLICT (id) DO NOTHING
        """)
        
        await session.execute(message_insert, {
            'id': str(message.id),
            'hub_id': hub.id,
            'content': processed_content,
            'image_url': image_url,
            'channel_id': str(message.channel.id),
            'guild_id': guild_id,
            'author_id': str(message.author.id),
            'created_at': current_time,
            'referred_message_id': referred_message_id,
            'reactions': None
        })
        
        # Bulk insert broadcasts if any exist
        if broadcast_message_ids:
            # Use VALUES clause for bulk insert - much faster than individual inserts
            broadcast_values = [
                {
                    'id': broadcast_id,
                    'message_id': str(message.id),
                    'channel_id': channel_id,
                    'created_at': current_time
                }
                for broadcast_id, channel_id in broadcast_message_ids
            ]
            
            broadcast_insert = text("""
                INSERT INTO broadcasts (id, message_id, channel_id, created_at)
                VALUES (:id, :message_id, :channel_id, :created_at)
                ON CONFLICT (id) DO NOTHING
            """)
            
            await session.execute(broadcast_insert, broadcast_values)

        await session.commit()
        
    except Exception as e:
        await session.rollback()
        raise RuntimeError(f"Failed to store message and broadcasts: {e}") from e


def validate_discord_id(discord_id: str) -> bool:
    """Validate that a Discord ID is a valid snowflake"""
    try:
        id_int = int(discord_id)
        # Discord snowflakes are 64-bit integers
        return 0 < id_int < (1 << 64)
    except (ValueError, TypeError):
        return False


def truncate_text(text: str, max_length: int, suffix: str = '...') -> str:
    """Safely truncate text with proper suffix handling"""
    if not text:
        return ''
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def clear_badge_cache():
    """Clear badge cache - call this periodically or when user data changes"""
    global _user_badge_cache
    _user_badge_cache.clear()
