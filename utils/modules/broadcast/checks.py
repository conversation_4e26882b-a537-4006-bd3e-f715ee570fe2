from datetime import datetime, <PERSON><PERSON><PERSON>
import json
from typing import TYPE_CHECKING, Optional, NamedTuple
import discord
from sqlalchemy import literal, select, and_, or_, union_all
from utils.modules.core.db.models import (
    Hub,
    Infraction,
    InfractionStatus,
    InfractionType,
    Blacklist,
    ServerBlacklist,
)
from utils.constants import logger
from utils.modules.core.db.redis import redis_client
from utils.modules.core.antiSpam import AntiSpamManager

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


class ValidationResult(NamedTuple):
    """Result of message validation checks"""

    is_valid: bool
    reason: Optional[str] = None
    should_notify: bool = False
    notification_message: Optional[str] = None
    infraction_id: Optional[str] = None
    spam_action: Optional[str] = None  # 'warn' or 'mute' for spam-related actions


class MessageValidator:
    """Handles all message validation checks before broadcasting"""

    def __init__(self, session: 'AsyncSession'):
        self.session = session
        self.redis = redis_client

    async def validate_message(
        self,
        message: discord.Message,
        hub: Hub,
        user_id: str,
        guild_id: str,
    ) -> ValidationResult:
        """
        Perform all validation checks for a message before broadcasting.
        Returns ValidationResult with validation status and notification details.
        """
        start_time = datetime.now()

        try:
            # 1. Check message length (2000 character limit)
            if is_message_too_long(message.content):
                logger.info(
                    f'Message blocked: exceeds 2000 characters ({len(message.content)} chars)'
                )
                return ValidationResult(
                    is_valid=False, reason='Message exceeds 2000 character limit'
                )

            # 2. Check if hub is locked
            if hub.locked:
                logger.info(f'Message blocked: hub {hub.id} is locked')
                return ValidationResult(is_valid=False, reason='Hub is currently locked')

            # 3. Check new account restriction (< 7 days old)
            if is_account_too_new(message.author.created_at):
                logger.info(
                    f'Message blocked: account too new ({(datetime.now() - message.author.created_at).days} days old)'
                )
                return ValidationResult(
                    is_valid=False,
                    reason='Account too new',
                    should_notify=True,
                    notification_message=(
                        'Your account is too new to send messages through InterChat. '
                        'Please wait a few more days and try again.'
                    ),
                )

            # 4. Check for spam detection (only for hub-connected channels)
            spam_manager = AntiSpamManager(self.session)
            spam_result = await spam_manager.check_spam(user_id, hub.id)

            if spam_result.is_spam:
                logger.info(
                    f'Spam detected: user {user_id} in hub {hub.id}, action: {spam_result.action_needed}'
                )
                return ValidationResult(
                    is_valid=False,
                    reason='Spam detected',
                    should_notify=True,
                    notification_message=spam_result.warning_message,
                    spam_action=spam_result.action_needed,
                )

            # 5. Batch check for all infractions and blacklists
            validation_result = await self._batch_validation_check(user_id, guild_id, hub.id)

            # Log performance
            elapsed = (datetime.now() - start_time).total_seconds() * 1000
            logger.debug(f'Message validation completed in {elapsed:.2f}ms')

            return validation_result

        except Exception as e:
            logger.error(f'Error during message validation: {e}')
            # Fail safe - allow message if validation fails
            return ValidationResult(is_valid=True)

    async def _batch_validation_check(
        self, user_id: str, guild_id: str, hub_id: str
    ) -> ValidationResult:
        # Check cache first
        cache_key = self._get_cache_key(user_id, guild_id, hub_id)
        cached_result = await self._get_validation_cache(cache_key)
        if cached_result:
            return cached_result

        now = datetime.now()

        # Single unified query using UNION ALL for all validation checks
        # to reduce database round trips from 4 to 1
        user_infraction_query = select(
            Infraction.id,
            Infraction.type,
            Infraction.reason,
            Infraction.expiresAt,
            literal('user_infraction').label('check_type'),
        ).where(
            and_(
                Infraction.userId == user_id,
                Infraction.hubId == hub_id,
                Infraction.status == InfractionStatus.ACTIVE,
                Infraction.type == InfractionType.BAN,
                or_(Infraction.expiresAt.is_(None), Infraction.expiresAt > now),
            )
        )

        server_infraction_query = select(
            Infraction.id,
            Infraction.type,
            Infraction.reason,
            Infraction.expiresAt,
            literal('server_infraction').label('check_type'),
        ).where(
            and_(
                Infraction.serverId == guild_id,
                Infraction.hubId == hub_id,
                Infraction.status == InfractionStatus.ACTIVE,
                Infraction.type == InfractionType.BAN,
                or_(Infraction.expiresAt.is_(None), Infraction.expiresAt > now),
            )
        )

        user_blacklist_query = select(
            Blacklist.id,
            literal(None).label('type'),  # Blacklist doesn't have type
            Blacklist.reason,
            Blacklist.expiresAt,
            literal('user_blacklist').label('check_type'),
        ).where(
            and_(
                Blacklist.userId == user_id,
                or_(Blacklist.expiresAt.is_(None), Blacklist.expiresAt > now),
            )
        )

        server_blacklist_query = select(
            ServerBlacklist.id,
            literal(None).label('type'),
            ServerBlacklist.reason,
            ServerBlacklist.expiresAt,
            literal('server_blacklist').label('check_type'),
        ).where(
            and_(
                ServerBlacklist.serverId == guild_id,
                or_(ServerBlacklist.expiresAt.is_(None), ServerBlacklist.expiresAt > now),
            )
        )

        # Combine all queries with UNION ALL and limit to first result
        combined_query = union_all(
            user_infraction_query,
            server_infraction_query,
            user_blacklist_query,
            server_blacklist_query,
        ).limit(1)  # Stop at first violation found

        # Execute single query
        result = (await self.session.execute(combined_query)).first()

        # Process result
        if result:
            violation_result = self._process_violation(result, user_id, guild_id, hub_id)
            # Cache negative results for shorter time (30 seconds)
            await self._cache_validation_result(cache_key, violation_result, ttl=30)
            return violation_result

        # All checks passed - cache positive result for longer (2 minutes)
        success_result = ValidationResult(is_valid=True)
        await self._cache_validation_result(cache_key, success_result, ttl=120)
        return success_result

    def _process_violation(
        self, result, user_id: str, guild_id: str, hub_id: str
    ) -> ValidationResult:
        """Process a validation violation result."""
        infraction_id, infraction_type, reason, expires_at, check_type = result

        if check_type == 'user_infraction':
            if expires_at is None:
                logger.info(f'Message blocked: user {user_id} permanently banned from hub {hub_id}')
                return ValidationResult(
                    is_valid=False,
                    reason='User is banned from this hub',
                    should_notify=not result.notified,
                    notification_message=f'You are banned from this hub. Reason: {reason}',
                    infraction_id=infraction_id,
                )
            else:
                logger.info(
                    f'Message blocked: user {user_id} temporarily banned from hub {hub_id} until {expires_at}'
                )
                return ValidationResult(
                    is_valid=False,
                    reason='User is temporarily banned from this hub',
                    should_notify=not result.notified,
                    notification_message=(
                        f'You are temporarily banned from this hub until <t:{int(expires_at.timestamp())}:F>. '
                        f'Reason: {reason}'
                    ),
                    infraction_id=infraction_id,
                )

        elif check_type == 'server_infraction':
            if expires_at is None:
                logger.info(
                    f'Message blocked: server {guild_id} permanently banned from hub {hub_id}'
                )
                return ValidationResult(
                    is_valid=False,
                    reason='Server is banned from this hub',
                    should_notify=not result.notified,
                    notification_message=f'Your server is banned from this hub. Reason: {reason}',
                    infraction_id=infraction_id,
                )
            else:
                logger.info(
                    f'Message blocked: server {guild_id} temporarily banned from hub {hub_id} until {expires_at}'
                )
                return ValidationResult(
                    is_valid=False,
                    reason='Server is temporarily banned from this hub',
                    should_notify=not result.notified,
                    notification_message=(
                        f'Your server is temporarily banned from this hub until <t:{int(expires_at.timestamp())}:F>. '
                        f'Reason: {reason}'
                    ),
                    infraction_id=infraction_id,
                )

        elif check_type == 'user_blacklist':
            logger.info(f'Message blocked: user {user_id} globally blacklisted')
            return ValidationResult(
                is_valid=False,
                reason='User is globally blacklisted',
                should_notify=True,
                notification_message=f'You are blacklisted from InterChat. Reason: {reason}',
                infraction_id=infraction_id,
            )

        elif check_type == 'server_blacklist':
            logger.info(f'Message blocked: server {guild_id} globally blacklisted')
            return ValidationResult(
                is_valid=False,
                reason='Server is globally blacklisted',
                should_notify=True,
                notification_message=f'Your server is blacklisted from InterChat. Reason: {reason}',
                infraction_id=infraction_id,
            )
        else:
            logger.error(f'Unknown check type: {check_type}')
            return ValidationResult(is_valid=True)

    # Redis caching methods
    async def _get_validation_cache(self, cache_key: str) -> Optional[ValidationResult]:
        """Get cached validation result from Redis."""
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                import json

                data = json.loads(cached_data)
                return ValidationResult(**data)
        except Exception as e:
            # Log cache miss but don't fail validation
            logger.warning(f'Redis cache get failed for {cache_key}: {e}')

        return None

    async def _cache_validation_result(self, cache_key: str, result: ValidationResult, ttl: int):
        """Cache validation result in Redis with TTL."""
        try:
            # Convert NamedTuple to dict for JSON serialization
            result_dict = result._asdict()
            await self.redis.setex(cache_key, ttl, json.dumps(result_dict))
        except Exception as e:
            # Log cache write failure but don't fail validation
            logger.warning(f'Redis cache set failed for {cache_key}: {e}')

    def _get_cache_key(self, user_id: str, guild_id: str, hub_id: str) -> str:
        """Generate consistent cache key for validation results."""
        return f'validation:{user_id}:{guild_id}:{hub_id}'


def is_message_too_long(content: str, max_length: int = 2000) -> bool:
    """Check if message content exceeds the maximum allowed length"""
    return len(content) > max_length


def is_account_too_new(created_at: datetime, min_age_days: int = 7) -> bool:
    """Check if account is newer than the minimum required age"""
    account_age = datetime.now(created_at.tzinfo) - created_at
    return account_age < timedelta(days=min_age_days)
