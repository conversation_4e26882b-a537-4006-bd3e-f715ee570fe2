from typing import TYPE_CHECKING
import discord
from discord.ext import commands

from utils.constants import InterchatConstants
from utils.modules.errors import customDiscord
from utils.modules.errors.errorHandler import error_handler

if TYPE_CHECKING:
    from main import Bot

constants = InterchatConstants()

async def interaction_check(
    interaction: discord.Interaction['Bot'],
    invoked: discord.User | discord.Member,
    interacted: discord.User | discord.Member,
) -> bool:
    if invoked.id != interacted.id:
        try:
            raise customDiscord.InteractionCheck()
        except Exception as e:
            await error_handler(interaction.client, interaction, e)
        return False
    return True


def is_interchat_staff_direct(
    bot: 'Bo<PERSON>',
    user: discord.User | discord.Member,
) -> bool:
    return user.id in bot.staff_ids


def is_interchat_staff(
    ctx: commands.Context['Bot'] | discord.Interaction['Bot'],
    user: discord.User | discord.Member | None = None,
) -> bool:
    bot = ctx.bot if isinstance(ctx, commands.Context) else ctx.client
    current_user = user

    if not user:
        current_user = ctx.author if isinstance(ctx, commands.Context) else ctx.user
    if not current_user:
        return False

    return current_user.id in constants.auth_users() or is_interchat_staff_direct(bot, current_user)


def is_interchat_staff_check():
    def predicate(ctx: commands.Context['Bot'] | discord.Interaction['Bot']):
        return is_interchat_staff(ctx)

    return commands.check(predicate)


async def permission_check(level): ...
