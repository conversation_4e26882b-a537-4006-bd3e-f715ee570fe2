import discord
from discord.ext import commands

from utils.constants import InterchatConstants, redis_client
from utils.modules.errors.customDiscord import RateLimited, WebhookRateLimit


async def message_rate_limit(ctx: commands.Context[commands.Bot]) -> bool:
    constants = InterchatConstants()

    limit = constants.rate_limits()['commands']['limit']
    period = constants.rate_limits()['commands']['period']

    key = f'command_rate_limit:{ctx.guild.id}:{ctx.author.id}'
    count = await redis_client.incr(key)

    if count == 1:
        await redis_client.expire(key, period)

    if count > limit:
        raise RateLimited()

    return True


async def webhook_rate_limit(channel: discord.abc.GuildChannel) -> bool:
    constants = InterchatConstants()

    limit = constants.rate_limits()['webhook']['limit']
    period = constants.rate_limits()['webhook']['period']

    key = f'webhook_rate_limit:{channel.id}'
    count = await redis_client.incr(key)

    if count == 1:
        await redis_client.expire(key, period)

    if count > limit:
        raise WebhookRateLimit()

    return True
