from typing import List, Optional

from sqlalchemy import (
    Column,
    Integer,
    String,
    Boolean,
    DateTime,
    Float,
    <PERSON><PERSON><PERSON>,
    JSO<PERSON>,
    Enum as SQLEnum,
    Table,
    Text,
    UniqueConstraint,
    Index,
    func,
    text,
    sql,
)
from sqlalchemy.orm import (
    relationship,
    Mapped,
    mapped_column,
    DeclarativeBase,
    MappedAsDataclass,
)
from sqlalchemy.dialects.postgresql import ARRAY
from datetime import datetime
import enum
from nanoid import generate

# NOTE: All collumns are nullable by default unless specified otherwise.


# Enums
class Role(enum.Enum):
    MODERATOR = 'MODERATOR'
    MANAGER = 'MANAGER'


class HubActivityLevel(enum.Enum):
    LOW = 'LOW'  # <10 messages/day
    MEDIUM = 'MEDIUM'  # 10-100 messages/day
    HIGH = 'HIGH'  # >100 messages/day


class InfractionType(enum.Enum):
    BAN = 'BAN'
    BLACKLIST = 'BLACKLIST'  #: Deprecated: Use BAN instead
    WARNING = 'WARNING'


class InfractionStatus(enum.Enum):
    ACTIVE = 'ACTIVE'
    REVOKED = 'REVOKED'
    APPEALED = 'APPEALED'


class AppealStatus(enum.Enum):
    PENDING = 'PENDING'
    ACCEPTED = 'ACCEPTED'
    REJECTED = 'REJECTED'


class BlockWordAction(enum.Enum):
    BLOCK_MESSAGE = 'BLOCK_MESSAGE'
    SEND_ALERT = 'SEND_ALERT'
    WARN = 'WARN'
    MUTE = 'MUTE'  # Temporary mute (with expiry)
    BAN = 'BAN'  # Permanent ban (no expiry)
    BLACKLIST = 'BLACKLIST'  #: Deprecated: Use BAN instead


class CallRatingStatus(enum.Enum):
    LIKE = 'LIKE'
    DISLIKE = 'DISLIKE'


class Badges(enum.Enum):
    VOTER = 'VOTER'  # For users who have voted on top.gg
    SUPPORTER = 'SUPPORTER'  # Premium supporters
    TRANSLATOR = 'TRANSLATOR'  # For users who help translate InterChat
    DEVELOPER = 'DEVELOPER'  # For users who contribute to the codebase
    STAFF = 'STAFF'  # For staff members who help manage the platform
    BETA_TESTER = 'BETA_TESTER'  # For users who participated in beta testing


class CallStatus(enum.Enum):
    QUEUED = 'QUEUED'
    ACTIVE = 'ACTIVE'
    ENDED = 'ENDED'


class ReportStatus(enum.Enum):
    PENDING = 'PENDING'
    RESOLVED = 'RESOLVED'
    IGNORED = 'IGNORED'


class BlacklistType(enum.Enum):
    PERMANENT = 'PERMANENT'
    TEMPORARY = 'TEMPORARY'


class LeaderboardPeriod(enum.Enum):
    DAILY = 'DAILY'
    WEEKLY = 'WEEKLY'
    MONTHLY = 'MONTHLY'
    ALL_TIME = 'ALL_TIME'


class LeaderboardType(enum.Enum):
    USER = 'USER'
    SERVER = 'SERVER'
    HUB = 'HUB'


class Base(MappedAsDataclass, DeclarativeBase):
    pass


# Association table for Hub-Tag many-to-many relationship
hub_tags = Table(
    '_HubToTag',  # Prisma’s auto‐gen name
    Base.metadata,
    Column('A', String(), ForeignKey('Hub.id'), primary_key=True),  # Hub is alphabetically first
    Column('B', String(), ForeignKey('Tag.id'), primary_key=True),  # Tag is second
)


class Hub(Base):
    __tablename__ = 'Hub'

    # Primary fields
    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    name: Mapped[str] = mapped_column(String(), unique=True)
    description: Mapped[str] = mapped_column(Text)
    ownerId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'))
    iconUrl: Mapped[str] = mapped_column(String())
    shortDescription: Mapped[Optional[str]] = mapped_column(String(100))

    # Relationships
    owner: Mapped['User'] = relationship(
        'User', back_populates='ownedHubs', lazy='noload', init=False
    )
    rulesAcceptances: Mapped[list['HubRulesAcceptance']] = relationship(
        'HubRulesAcceptance', back_populates='hub', lazy='noload', init=False
    )
    moderators: Mapped[list['HubModerator']] = relationship(
        'HubModerator', back_populates='hub', lazy='noload', init=False
    )
    connections: Mapped[list['Connection']] = relationship(
        'Connection', back_populates='hub', lazy='noload', init=False
    )
    tags: Mapped[list['Tag']] = relationship(
        'Tag', secondary=hub_tags, back_populates='hubs', lazy='noload', init=False
    )
    upvotes: Mapped[list['HubUpvote']] = relationship(
        'HubUpvote', back_populates='hub', lazy='noload', init=False
    )
    reviews: Mapped[list['HubReview']] = relationship(
        'HubReview', back_populates='hub', lazy='noload', init=False
    )
    logConfig: Mapped['HubLogConfig | None'] = relationship(
        'HubLogConfig', back_populates='hub', lazy='noload', init=False
    )
    blockWords: Mapped[list['BlockWord']] = relationship(
        'BlockWord', back_populates='hub', lazy='noload', init=False
    )
    antiSwearRules: Mapped[list['AntiSwearRule']] = relationship(
        'AntiSwearRule', back_populates='hub', lazy='noload', init=False
    )
    infractions: Mapped[list['Infraction']] = relationship(
        'Infraction', back_populates='hub', lazy='noload', init=False
    )
    invites: Mapped[list['HubInvite']] = relationship(
        'HubInvite', back_populates='hub', lazy='noload', init=False
    )
    messages: Mapped[list['Message']] = relationship(
        'Message', back_populates='hub', lazy='noload', init=False
    )
    reports: Mapped[list['Report']] = relationship(
        'Report', back_populates='hub', lazy='noload', init=False
    )
    activityMetrics: Mapped['HubActivityMetrics | None'] = relationship(
        'HubActivityMetrics', back_populates='hub', lazy='noload', init=False
    )
    leaderboardEntries: Mapped[list['LeaderboardEntry']] = relationship(
        'LeaderboardEntry', back_populates='hub', lazy='noload', init=False
    )

    # Timestamps
    createdAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), nullable=False
    )
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now(), nullable=False
    )
    lastActive: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), nullable=False
    )

    # Optional fields
    bannerUrl: Mapped[Optional[str]] = mapped_column(
        String(), default=None, server_default=sql.null()
    )
    welcomeMessage: Mapped[Optional[str]] = mapped_column(
        Text(), default=None, server_default=sql.null()
    )
    language: Mapped[Optional[str]] = mapped_column(
        String(), default=None, server_default=sql.null()
    )
    region: Mapped[Optional[str]] = mapped_column(String(), default=None, server_default=sql.null())

    # Numeric fields
    settings: Mapped[int] = mapped_column(
        Integer(), nullable=False, default=0, server_default=text('0')
    )
    appealCooldownHours: Mapped[int] = mapped_column(
        Integer(), default=168, server_default=text('168'), nullable=False
    )
    weeklyMessageCount: Mapped[int] = mapped_column(
        Integer(), default=0, server_default=text('0'), nullable=False
    )

    # Boolean flags
    private: Mapped[bool] = mapped_column(
        Boolean(), default=True, server_default=sql.true(), nullable=False
    )
    locked: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false(), nullable=False
    )
    nsfw: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false(), nullable=False
    )
    verified: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false(), nullable=False
    )
    partnered: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false(), nullable=False
    )
    featured: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false(), nullable=False
    )

    # Array field (PostgreSQL specific)
    rules: Mapped[list[str]] = mapped_column(ARRAY(String), default=list, nullable=False)

    # Enum field
    activityLevel: Mapped[HubActivityLevel] = mapped_column(
        SQLEnum(
            HubActivityLevel,
            name='HubActivityLevel',
            create_type=False,
        ),
        default=HubActivityLevel.LOW,
        server_default=HubActivityLevel.LOW.value,
        nullable=False,
    )

    # Indexes
    __table_args__ = (
        Index('Hub_ownerId_idx', 'ownerId'),
        Index('Hub_verified_featured_private_idx', 'verified', 'featured', 'private'),
        Index('Hub_activityLevel_idx', 'activityLevel'),
        Index('Hub_language_idx', 'language'),
        Index('Hub_nsfw_idx', 'nsfw'),
        Index('Hub_weeklyMessageCount_idx', 'weeklyMessageCount'),
    )


class Tag(Base):
    __tablename__ = 'Tag'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    name: Mapped[str] = mapped_column(String(), unique=True, nullable=False)
    category: Mapped[Optional[str]] = mapped_column(
        String()
    )  # e.g., "Gaming", "Technology", "Art", "Music"
    description: Mapped[Optional[str]] = mapped_column(String())
    color: Mapped[Optional[str]] = mapped_column(String())  # Hex color for UI display

    # Many-to-many relationship with Hub
    hubs: Mapped[list['Hub']] = relationship(
        'Hub', secondary=hub_tags, back_populates='tags', lazy='noload'
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    isOfficial: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false()
    )  # Official InterChat tags vs user-created
    usageCount: Mapped[int] = mapped_column(
        Integer(), default=0, server_default=text('0')
    )  # Track popularity for autocomplete

    __table_args__ = (
        Index('Tag_category_idx', 'category'),
        Index('Tag_usageCount_idx', 'usageCount'),
    )


class HubUpvote(Base):
    __tablename__ = 'HubUpvote'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    userId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'), nullable=False)
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    hubId: Mapped[str] = mapped_column(
        String(),
        ForeignKey('Hub.id', ondelete='CASCADE'),
        nullable=False,
    )

    hub: Mapped['Hub'] = relationship('Hub', back_populates='upvotes', lazy='noload', init=False)
    user: Mapped['User'] = relationship(
        'User', back_populates='upvotedHubs', lazy='noload', init=False
    )

    __table_args__ = (
        UniqueConstraint('hubId', 'userId'),
        Index('HubUpvote_userId_idx', 'userId'),
    )


class HubReview(Base):
    __tablename__ = 'HubReview'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    rating: Mapped[int] = mapped_column(Integer())  # Rating from 1 to 5
    text: Mapped[str] = mapped_column(String())  # Review text

    hubId: Mapped[str] = mapped_column(
        String(),
        ForeignKey('Hub.id', ondelete='CASCADE'),
        nullable=False,
    )
    userId: Mapped[str] = mapped_column(
        String(),
        ForeignKey('User.id'),
        nullable=False,
    )

    hub: Mapped['Hub'] = relationship('Hub', back_populates='reviews', lazy='noload', init=False)
    user: Mapped['User'] = relationship('User', back_populates='reviews', lazy='noload', init=False)

    __table_args__ = (
        UniqueConstraint('hubId', 'userId'),
        Index('HubReview_userId_idx', 'userId'),
    )


class HubModerator(Base):
    __tablename__ = 'HubModerator'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(String(), ForeignKey('Hub.id', ondelete='CASCADE'))
    userId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'))
    role: Mapped[Optional[Role]] = mapped_column(
        SQLEnum(Role, name='Role', create_type=False),
        default=Role.MODERATOR,
    )

    hub: Mapped['Hub'] = relationship('Hub', back_populates='moderators', lazy='noload', init=False)
    user: Mapped['User'] = relationship(
        'User', back_populates='modPositions', lazy='noload', init=False
    )

    __table_args__ = (
        UniqueConstraint('hubId', 'userId'),
        Index('HubModerator_userId_idx', 'userId'),
    )


class Connection(Base):
    __tablename__ = 'Connection'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    channelId: Mapped[str] = mapped_column(String(), unique=True)
    invite: Mapped[Optional[str]] = mapped_column(String())
    webhookURL: Mapped[str] = mapped_column(String())
    serverId: Mapped[str] = mapped_column(String(), ForeignKey('ServerData.id'))
    hubId: Mapped[str] = mapped_column(String(), ForeignKey('Hub.id', ondelete='CASCADE'))
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    lastActive: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    parentId: Mapped[Optional[str]] = mapped_column(
        String(), default=None, server_default=sql.null()
    )  # Parent channel ID for threads

    connected: Mapped[bool] = mapped_column(Boolean(), default=True, server_default=sql.true())
    server: Mapped['ServerData'] = relationship(
        'ServerData', back_populates='connections', lazy='noload', init=False
    )
    hub: Mapped['Hub'] = relationship(
        'Hub', back_populates='connections', lazy='noload', init=False
    )

    __table_args__ = (
        UniqueConstraint('channelId', 'serverId'),
        UniqueConstraint('hubId', 'serverId'),
        Index('Connection_serverId_idx', 'serverId'),
        Index('Connection_hubId_idx', 'hubId'),
        Index('Connection_hubId_channelId_idx', 'hubId', 'channelId'),
        Index('Connection_channelId_connected_idx', 'channelId', 'connected'),
        Index('Connection_hubId_connected_idx', 'hubId', 'connected'),
        Index('Connection_lastActive_idx', 'lastActive'),
    )


class Infraction(Base):
    __tablename__ = 'Infraction'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(size=10), init=False
    )
    hubId: Mapped[str] = mapped_column(String(), ForeignKey('Hub.id'))
    moderatorId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'))
    reason: Mapped[str] = mapped_column(String())
    expiresAt: Mapped[Optional[datetime]] = mapped_column(DateTime())

    # For user infractions
    userId: Mapped[Optional[str]] = mapped_column(String(), ForeignKey('User.id'))

    # For server infractions
    serverId: Mapped[Optional[str]] = mapped_column(String(), ForeignKey('ServerData.id'))
    serverName: Mapped[Optional[str]] = mapped_column(String())

    hub: Mapped['Hub'] = relationship(
        'Hub', back_populates='infractions', lazy='noload', init=False
    )
    moderator: Mapped['User'] = relationship(
        'User',
        foreign_keys=[moderatorId],
        back_populates='issuedInfractions',
        lazy='noload',
        init=False,
    )
    user: Mapped['User'] = relationship(
        'User',
        foreign_keys=[userId],
        back_populates='infractions',
        lazy='noload',
        init=False,
    )
    server: Mapped['ServerData'] = relationship(
        'ServerData', back_populates='infractions', lazy='noload', init=False
    )
    appeals: Mapped[list['Appeal']] = relationship(
        'Appeal', back_populates='infraction', lazy='noload', init=False
    )
    createdAt: Mapped[datetime] = mapped_column(
        DateTime(),
        server_default=func.now(),
        default=None,
    )
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(),
        server_default=func.now(),
        onupdate=func.now(),
        default=None,
    )

    type: Mapped[InfractionType] = mapped_column(
        SQLEnum(InfractionType, name='InfractionType', create_type=False),
        default=InfractionType.BAN,
        server_default=InfractionType.BAN.value,
    )
    status: Mapped[InfractionStatus] = mapped_column(
        SQLEnum(InfractionStatus, name='InfractionStatus', create_type=False),
        default=InfractionStatus.ACTIVE,
        server_default=InfractionStatus.ACTIVE.value,
    )
    notified: Mapped[bool] = mapped_column(Boolean(), default=False, server_default=sql.false())

    __table_args__ = (
        Index('Infraction_status_hubId_idx', 'status', 'hubId'),
        Index('Infraction_userId_idx', 'userId'),
        Index('Infraction_serverId_idx', 'serverId'),
        Index('Infraction_type_idx', 'type'),
        Index('Infraction_expiresAt_idx', 'expiresAt'),
        # Composite index for user infractions lookup
        Index(
            'Infraction_userId_hubId_status_type_expiresAt_idx',
            'userId',
            'hubId',
            'status',
            'type',
            'expiresAt',
            postgresql_where=(userId.isnot(None)),  # Partial index for PostgreSQL
        ),
        # Composite index for server infractions lookup
        Index(
            'Infraction_serverId_hubId_status_type_expiresAt_idx',
            'serverId',
            'hubId',
            'status',
            'type',
            'expiresAt',
            postgresql_where=(serverId.isnot(None)),  # Partial index for PostgreSQL
        ),
    )


class Appeal(Base):
    __tablename__ = 'Appeal'

    infractionId: Mapped[str] = mapped_column(String(), ForeignKey('Infraction.id'))
    userId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'))
    reason: Mapped[str] = mapped_column(String())
    infraction: Mapped['Infraction'] = relationship(
        'Infraction', back_populates='appeals', lazy='noload'
    )
    user: Mapped['User'] = relationship('User', back_populates='appeals', lazy='noload')
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    status: Mapped[AppealStatus] = mapped_column(
        SQLEnum(
            AppealStatus,
            name='AppealStatus',
            create_type=False,
        ),
        default=AppealStatus.PENDING,
        server_default=AppealStatus.PENDING.value,
    )

    __table_args__ = (
        Index('Appeal_infractionId_idx', 'infractionId'),
        Index('Appeal_userId_idx', 'userId'),
        Index('Appeal_status_idx', 'status'),
        Index('Appeal_createdAt_idx', 'createdAt'),
    )


class BlockWord(Base):
    __tablename__ = 'BlockWord'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        String(), ForeignKey('Hub.id', ondelete='CASCADE'), nullable=False
    )
    name: Mapped[str] = mapped_column(String(), nullable=False)
    createdBy: Mapped[str] = mapped_column(String(), ForeignKey('User.id'), nullable=False)
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), init=False)
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(),
        server_default=func.now(),
        onupdate=func.now(),
        init=False,
    )

    # Relations
    words: Mapped[str] = mapped_column(String(), nullable=False)  # separated by comma (,)
    actions: Mapped[list[BlockWordAction]] = mapped_column(
        ARRAY(SQLEnum(BlockWordAction, name='BlockWordAction', create_type=False)),
    )

    hub: Mapped['Hub'] = relationship('Hub', back_populates='blockWords', lazy='noload')
    creator: Mapped['User'] = relationship(
        'User', back_populates='blockWordsCreated', lazy='noload'
    )

    __table_args__ = (
        UniqueConstraint('hubId', 'name'),
        Index('BlockWord_hubId_idx', 'hubId'),
    )


class AntiSwearRule(Base):
    __tablename__ = 'AntiSwearRule'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        String(), ForeignKey('Hub.id', ondelete='CASCADE'), nullable=False
    )
    name: Mapped[str] = mapped_column(String(), nullable=False)
    createdBy: Mapped[str] = mapped_column(String(), ForeignKey('User.id'), nullable=False)
    # Duration configuration for time-based actions (in minutes)
    muteDurationMinutes: Mapped[Optional[int]] = mapped_column(
        Integer()
    )  # Duration for MUTE action
    blacklistDurationMinutes: Mapped[Optional[int]] = mapped_column(
        Integer()
    )  # Duration for BLACKLIST action

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    actions: Mapped[list[BlockWordAction]] = mapped_column(
        ARRAY(
            SQLEnum(
                BlockWordAction,
                name='BlockWordAction',
                create_type=False,
                server_default=BlockWordAction.BLOCK_MESSAGE.value,
            )
        ),
        default=list,
        server_default=text('\'{}\'::"BlockWordAction"[]'),
    )
    logViolations: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false()
    )  # Enable/disable logging

    hub: Mapped['Hub'] = relationship(
        'Hub', back_populates='antiSwearRules', lazy='noload', init=False
    )
    creator: Mapped['User'] = relationship(
        'User', back_populates='antiSwearRulesCreated', lazy='noload', init=False
    )
    patterns: Mapped[list['AntiSwearPattern']] = relationship(
        'AntiSwearPattern',
        back_populates='rule',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )

    __table_args__ = (
        UniqueConstraint('hubId', 'name'),
        Index('AntiSwearRule_hubId_idx', 'hubId'),
    )


class AntiSwearPattern(Base):
    __tablename__ = 'AntiSwearPattern'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    ruleId: Mapped[str] = mapped_column(
        String(), ForeignKey('AntiSwearRule.id', ondelete='CASCADE'), nullable=False
    )
    pattern: Mapped[str] = mapped_column(String(), nullable=False)  # Individual word or pattern
    isRegex: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false()
    )  # For future extensibility

    rule: Mapped['AntiSwearRule'] = relationship(
        'AntiSwearRule', back_populates='patterns', lazy='noload', init=False
    )

    __table_args__ = (Index('AntiSwearPattern_ruleId_idx', 'ruleId'),)


class HubLogConfig(Base):
    __tablename__ = 'HubLogConfig'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        String(), ForeignKey('Hub.id', ondelete='CASCADE'), unique=True, nullable=False
    )
    modLogsChannelId: Mapped[str] = mapped_column(
        String(), nullable=True, server_default=sql.null()
    )
    modLogsRoleId: Mapped[str] = mapped_column(String(), nullable=True, server_default=sql.null())
    joinLeavesChannelId: Mapped[str] = mapped_column(
        String(), nullable=True, server_default=sql.null()
    )
    joinLeavesRoleId: Mapped[str] = mapped_column(
        String(), nullable=True, server_default=sql.null()
    )
    appealsChannelId: Mapped[str] = mapped_column(
        String(), nullable=True, server_default=sql.null()
    )
    appealsRoleId: Mapped[str] = mapped_column(String(), nullable=True, server_default=sql.null())
    reportsChannelId: Mapped[str] = mapped_column(
        String(), nullable=True, server_default=sql.null()
    )
    reportsRoleId: Mapped[str] = mapped_column(String(), nullable=True, server_default=sql.null())
    networkAlertsChannelId: Mapped[str] = mapped_column(
        String(), nullable=True, server_default=sql.null()
    )
    networkAlertsRoleId: Mapped[str] = mapped_column(
        String(), nullable=True, server_default=sql.null()
    )
    messageModerationChannelId: Mapped[str] = mapped_column(
        String(), nullable=True, server_default=sql.null()
    )
    messageModerationRoleId: Mapped[str] = mapped_column(
        String(), nullable=True, server_default=sql.null()
    )

    hub: Mapped['Hub'] = relationship('Hub', back_populates='logConfig', lazy='noload')
    __table_args__ = (Index('HubLogConfig_hubId_idx', 'hubId'),)


class HubInvite(Base):
    __tablename__ = 'HubInvite'

    hubId: Mapped[str] = mapped_column(
        String(), ForeignKey('Hub.id', ondelete='CASCADE'), nullable=False
    )
    expires: Mapped[Optional[datetime]] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )
    code: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(size=7), unique=True
    )
    createdAt: Mapped[datetime] = mapped_column(DateTime(), default=None, server_default=func.now())

    maxUses: Mapped[int] = mapped_column(
        Integer(), default=0, server_default=text('0')
    )  # 0 = unlimited
    uses: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))

    hub: Mapped['Hub'] = relationship('Hub', back_populates='invites', lazy='noload', init=False)

    __table_args__ = (Index('HubInvite_hubId_idx', 'hubId'),)


class HubRulesAcceptance(Base):
    __tablename__ = 'HubRulesAcceptance'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    userId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'), nullable=False)
    hubId: Mapped[str] = mapped_column(
        String(), ForeignKey('Hub.id', ondelete='CASCADE'), nullable=False
    )
    acceptedAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())

    user: Mapped['User'] = relationship(
        'User', back_populates='rulesAcceptances', lazy='noload', init=False
    )
    hub: Mapped['Hub'] = relationship(
        'Hub', back_populates='rulesAcceptances', lazy='noload', init=False
    )

    __table_args__ = (
        UniqueConstraint('userId', 'hubId'),
        Index('HubRulesAcceptance_hubId_userId_idx', 'hubId', 'userId'),
    )


class DonationTierDefinition(Base):
    __tablename__ = 'DonationTierDefinition'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    name: Mapped[str] = mapped_column(String(), unique=True, nullable=False)
    description: Mapped[str] = mapped_column(String(), nullable=False)
    price: Mapped[float] = mapped_column(Float(), nullable=False)

    donations: Mapped[list['Donation']] = relationship(
        'Donation', back_populates='donationTier', lazy='noload'
    )
    users: Mapped[list['User']] = relationship('User', back_populates='donationTier', lazy='noload')

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )


class Donation(Base):
    __tablename__ = 'Donation'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    kofiTransactionId: Mapped[str] = mapped_column(
        String(), unique=True, nullable=False
    )  # Ko-fi transaction ID
    messageId: Mapped[str] = mapped_column(
        String(), unique=True, nullable=False
    )  # Ko-fi message ID
    amount: Mapped[float] = mapped_column(Float(), nullable=False)  # Donation amount
    currency: Mapped[str] = mapped_column(String(), nullable=False)  # Currency code (e.g., "USD")
    fromName: Mapped[str] = mapped_column(String(), nullable=False)  # Donor name from Ko-fi
    message: Mapped[Optional[str]] = mapped_column(String())  # Optional donation message
    email: Mapped[str] = mapped_column(String())  # Donor email (if provided)
    kofiTimestamp: Mapped[datetime] = mapped_column(
        DateTime(), nullable=False
    )  # Timestamp from Ko-fi
    kofiUrl: Mapped[str] = mapped_column(String())  # Ko-fi donation URL

    # Subscription-specific fields
    donationTierId: Mapped[str] = mapped_column(String(), ForeignKey('DonationTierDefinition.id'))
    discordUserId: Mapped[str] = mapped_column(
        String(), ForeignKey('User.id')
    )  # Linked Discord user ID

    discordUser: Mapped['User'] = relationship('User', back_populates='donations', lazy='noload')
    donationTier: Mapped['DonationTierDefinition'] = relationship(
        'DonationTierDefinition', back_populates='donations', lazy='noload'
    )
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )

    isPublic: Mapped[bool] = mapped_column(
        Boolean(), default=True, server_default=sql.true()
    )  # Whether donation is public
    processed: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false()
    )  # Whether premium benefits have been granted

    __table_args__ = (
        Index('Donation_kofiTransactionId_idx', 'kofiTransactionId'),
        Index('Donation_discordUserId_idx', 'discordUserId'),
        Index('Donation_createdAt_idx', 'createdAt'),
    )


class PendingClaim(Base):
    __tablename__ = 'PendingClaim'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    kofiTransactionId: Mapped[str] = mapped_column(
        String(), unique=True, nullable=False
    )  # Reference to Ko-fi transaction
    email: Mapped[str] = mapped_column(String(), nullable=False)  # Email from Ko-fi donation
    tierName: Mapped[str] = mapped_column(String())  # Ko-fi tier name
    amount: Mapped[float] = mapped_column(Float(), nullable=False)  # Donation amount
    currency: Mapped[str] = mapped_column(String(), nullable=False)  # Currency code
    fromName: Mapped[str] = mapped_column(String(), nullable=False)  # Donor name from Ko-fi
    expiresAt: Mapped[datetime] = mapped_column(
        DateTime(), nullable=False
    )  # When this claim expires
    claimedBy: Mapped[str] = mapped_column(String())  # Discord user ID who claimed it
    claimedAt: Mapped[datetime] = mapped_column(DateTime)
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    claimed: Mapped[bool] = mapped_column(Boolean(), default=False, server_default=sql.false())

    __table_args__ = (
        Index('PendingClaim_email_idx', 'email'),
        Index('PendingClaim_claimed_idx', 'claimed'),
        Index('PendingClaim_expiresAt_idx', 'expiresAt'),
        Index('PendingClaim_kofiTransactionId_idx', 'kofiTransactionId'),
    )


class HubActivityMetrics(Base):
    __tablename__ = 'HubActivityMetrics'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        String(), ForeignKey('Hub.id', ondelete='CASCADE'), unique=True, nullable=False
    )
    hub: Mapped['Hub'] = relationship('Hub', back_populates='activityMetrics', lazy='noload')
    lastUpdated: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())

    # Daily metrics
    messagesLast24h: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    activeUsersLast24h: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    newConnectionsLast24h: Mapped[int] = mapped_column(
        Integer(), default=0, server_default=text('0')
    )

    # Weekly metrics
    messagesLast7d: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    activeUsersLast7d: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    newConnectionsLast7d: Mapped[int] = mapped_column(
        Integer(), default=0, server_default=text('0')
    )

    # Growth metrics
    memberGrowthRate: Mapped[float] = mapped_column(
        Float, default=0.0, server_default=text('0.0')
    )  # Percentage growth over 7 days
    engagementRate: Mapped[float] = mapped_column(
        Float, default=0.0, server_default=text('0.0')
    )  # Messages per active user

    # Timestamps

    __table_args__ = (
        Index('HubActivityMetrics_hubId_idx', 'hubId'),
        Index('HubActivityMetrics_lastUpdated_idx', 'lastUpdated'),
    )


class Announcement(Base):
    __tablename__ = 'Announcement'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    title: Mapped[str] = mapped_column(String(), nullable=False)
    content: Mapped[str] = mapped_column(String(), nullable=False)
    imageUrl: Mapped[Optional[str]] = mapped_column(
        String(), default=None, server_default=sql.null()
    )
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), init=False)
    thumbnailUrl: Mapped[Optional[str]] = mapped_column(
        String(), default=None, server_default=sql.null()
    )


class ServerData(Base):
    __tablename__ = 'ServerData'

    id: Mapped[str] = mapped_column(String(), primary_key=True)
    name: Mapped[str] = mapped_column(String())
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )

    # Relations
    connections: Mapped[List['Connection']] = relationship(
        'Connection', back_populates='server', lazy='noload', init=False
    )
    infractions: Mapped[List['Infraction']] = relationship(
        'Infraction', back_populates='server', lazy='noload', init=False
    )
    serverBlacklists: Mapped[List['ServerBlacklist']] = relationship(
        'ServerBlacklist', back_populates='server', lazy='noload', init=False
    )
    leaderboardEntries: Mapped[List['LeaderboardEntry']] = relationship(
        'LeaderboardEntry',
        back_populates='server',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )

    lastMessageAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    inviteCode: Mapped[Optional[str]] = mapped_column(
        String(), default=None, server_default=sql.null()
    )
    messageCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    premiumStatus: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false()
    )
    iconUrl: Mapped[Optional[str]] = mapped_column(
        String(), default=None, server_default=sql.null()
    )


class Call(Base):
    __tablename__ = 'Call'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    initiatorId: Mapped[str] = mapped_column(String(), nullable=False)
    endTime: Mapped[datetime] = mapped_column(DateTime)
    startTime: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )

    # Relations
    participants: Mapped[List['CallParticipant']] = relationship(
        'CallParticipant',
        back_populates='call',
        lazy='noload',
        cascade='all, delete-orphan',
    )
    messages: Mapped[List['CallMessage']] = relationship(
        'CallMessage',
        back_populates='call',
        lazy='noload',
        cascade='all, delete-orphan',
    )
    ratings: Mapped[List['CallRating']] = relationship(
        'CallRating', back_populates='call', lazy='noload', cascade='all, delete-orphan'
    )

    status: Mapped[CallStatus] = mapped_column(
        SQLEnum(CallStatus, name='CallStatus', create_type=False),
        default=CallStatus.QUEUED,
        server_default=CallStatus.QUEUED.value,
    )

    __table_args__ = (
        Index('Call_status_idx', 'status'),
        Index('Call_startTime_idx', 'startTime'),
        Index('Call_initiatorId_idx', 'initiatorId'),
    )


class CallParticipant(Base):
    __tablename__ = 'CallParticipant'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    callId: Mapped[str] = mapped_column(
        String(), ForeignKey('Call.id', ondelete='CASCADE'), nullable=False
    )
    call: Mapped['Call'] = relationship('Call', back_populates='participants', lazy='noload')
    users: Mapped[List['CallParticipantUser']] = relationship(
        'CallParticipantUser',
        back_populates='participant',
        lazy='noload',
        cascade='all, delete-orphan',
    )
    leftAt: Mapped[datetime] = mapped_column(DateTime)
    joinedAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    channelId: Mapped[str] = mapped_column(String(), nullable=False)
    guildId: Mapped[str] = mapped_column(String(), nullable=False)
    webhookUrl: Mapped[str] = mapped_column(String(), nullable=False)
    messageCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))

    __table_args__ = (
        UniqueConstraint('callId', 'channelId'),
        Index('CallParticipant_callId_idx', 'callId'),
        Index('CallParticipant_channelId_idx', 'channelId'),
        Index('CallParticipant_guildId_idx', 'guildId'),
    )


class CallParticipantUser(Base):
    __tablename__ = 'CallParticipantUser'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    leftAt: Mapped[datetime] = mapped_column(DateTime)
    participantId: Mapped[str] = mapped_column(
        String(), ForeignKey('CallParticipant.id', ondelete='CASCADE'), nullable=False
    )
    participant: Mapped['CallParticipant'] = relationship(
        'CallParticipant', back_populates='users', lazy='noload'
    )
    userId: Mapped[str] = mapped_column(String(), nullable=False)
    joinedAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())

    __table_args__ = (
        UniqueConstraint('participantId', 'userId'),
        Index('CallParticipantUser_participantId_idx', 'participantId'),
        Index('CallParticipantUser_userId_idx', 'userId'),
    )


class CallMessage(Base):
    __tablename__ = 'CallMessage'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    callId: Mapped[str] = mapped_column(
        String(), ForeignKey('Call.id', ondelete='CASCADE'), nullable=False
    )
    authorId: Mapped[str] = mapped_column(String(), nullable=False)
    authorUsername: Mapped[str] = mapped_column(String(), nullable=False)
    content: Mapped[str] = mapped_column(String(), nullable=False)
    attachmentUrl: Mapped[str] = mapped_column(String())
    call: Mapped['Call'] = relationship('Call', back_populates='messages', lazy='noload')
    timestamp: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())

    __table_args__ = (
        Index('CallMessage_callId_idx', 'callId'),
        Index('CallMessage_authorId_idx', 'authorId'),
        Index('CallMessage_timestamp_idx', 'timestamp'),
    )


class CallRating(Base):
    __tablename__ = 'CallRating'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    callId: Mapped[str] = mapped_column(
        String(), ForeignKey('Call.id', ondelete='CASCADE'), nullable=False
    )
    raterId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'), nullable=False)
    targetId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'), nullable=False)
    rating: Mapped[str] = mapped_column(
        SQLEnum(CallRatingStatus, name='CallRatingStatus', create_type=False),
        nullable=False,
    )
    call: Mapped['Call'] = relationship('Call', back_populates='ratings', lazy='noload')
    rater: Mapped['User'] = relationship(
        'User', foreign_keys=[raterId], back_populates='ratingsMade', lazy='noload'
    )
    target: Mapped['User'] = relationship(
        'User', foreign_keys=[targetId], back_populates='ratingsReceived', lazy='noload'
    )

    timestamp: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())

    __table_args__ = (
        UniqueConstraint('callId', 'raterId', 'targetId'),
        Index('CallRating_targetId_idx', 'targetId'),
        Index('CallRating_raterId_idx', 'raterId'),
        Index('CallRating_callId_idx', 'callId'),
    )


class ReputationLog(Base):
    __tablename__ = 'ReputationLog'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    giverId: Mapped[str] = mapped_column(String(), nullable=False)
    receiverId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'), nullable=False)
    reason: Mapped[str] = mapped_column(String(), nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    receiver: Mapped['User'] = relationship('User', back_populates='reputationLog', lazy='noload')
    automatic: Mapped[bool] = mapped_column(Boolean(), default=False, server_default=sql.false())

    __table_args__ = (
        Index('ReputationLog_receiverId_idx', 'receiverId'),
        Index('ReputationLog_giverId_idx', 'giverId'),
    )


class Account(Base):
    __tablename__ = 'Account'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    userId: Mapped[str] = mapped_column(
        String(), ForeignKey('User.id', ondelete='CASCADE'), nullable=False
    )
    type: Mapped[str] = mapped_column(String(), nullable=False)
    provider: Mapped[str] = mapped_column(String(), nullable=False)
    providerAccountId: Mapped[str] = mapped_column(String(), nullable=False)
    refresh_token: Mapped[str] = mapped_column(String(), nullable=True)
    access_token: Mapped[str] = mapped_column(String(), nullable=True)
    expires_at: Mapped[int] = mapped_column(Integer(), nullable=True)
    token_type: Mapped[str] = mapped_column(String(), nullable=True)
    scope: Mapped[str] = mapped_column(String(), nullable=True)
    id_token: Mapped[str] = mapped_column(String(), nullable=True)
    user: Mapped['User'] = relationship('User', back_populates='accounts', lazy='noload')

    session_state: Mapped[Optional[str]] = mapped_column(
        String(), default=None, server_default=sql.null()
    )

    __table_args__ = (UniqueConstraint('provider', 'providerAccountId'),)


class Session(Base):
    __tablename__ = 'Session'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    sessionToken: Mapped[str] = mapped_column(String(), unique=True, nullable=False)
    userId: Mapped[str] = mapped_column(
        String(), ForeignKey('User.id', ondelete='CASCADE'), nullable=False
    )
    expires: Mapped[datetime] = mapped_column(DateTime(), nullable=False)

    user: Mapped['User'] = relationship('User', back_populates='sessions', lazy='noload')


class Achievement(Base):
    __tablename__ = 'Achievement'

    id: Mapped[str] = mapped_column(String(), primary_key=True)
    name: Mapped[str] = mapped_column(String(), nullable=False)
    description: Mapped[str] = mapped_column(String(), nullable=False)
    badgeEmoji: Mapped[str] = mapped_column(String(), nullable=False)
    badgeUrl: Mapped[str] = mapped_column(String())

    # Relations
    userAchievements: Mapped[List['UserAchievement']] = relationship(
        'UserAchievement',
        back_populates='achievement',
        lazy='noload',
        cascade='all, delete-orphan',
    )
    userProgress: Mapped[List['UserAchievementProgress']] = relationship(
        'UserAchievementProgress',
        back_populates='achievement',
        lazy='noload',
        cascade='all, delete-orphan',
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    threshold: Mapped[int] = mapped_column(Integer(), default=1)
    secret: Mapped[bool] = mapped_column(Boolean(), default=False, server_default=sql.false())


class UserAchievement(Base):
    __tablename__ = 'UserAchievement'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    userId: Mapped[str] = mapped_column(
        String(), ForeignKey('User.id', ondelete='CASCADE'), nullable=False
    )
    achievementId: Mapped[str] = mapped_column(
        String(), ForeignKey('Achievement.id', ondelete='CASCADE'), nullable=False
    )
    user: Mapped['User'] = relationship('User', back_populates='achievements', lazy='noload')
    achievement: Mapped['Achievement'] = relationship(
        'Achievement', back_populates='userAchievements', lazy='noload'
    )
    unlockedAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())

    __table_args__ = (
        UniqueConstraint('userId', 'achievementId'),
        Index('UserAchievement_userId_idx', 'userId'),
        Index('UserAchievement_achievementId_idx', 'achievementId'),
    )


class UserAchievementProgress(Base):
    __tablename__ = 'UserAchievementProgress'

    userId: Mapped[str] = mapped_column(
        String(), ForeignKey('User.id', ondelete='CASCADE'), primary_key=True
    )
    achievementId: Mapped[str] = mapped_column(
        String(), ForeignKey('Achievement.id', ondelete='CASCADE'), primary_key=True
    )

    user: Mapped['User'] = relationship('User', back_populates='achievementProgress', lazy='noload')
    achievement: Mapped['Achievement'] = relationship(
        'Achievement', back_populates='userProgress', lazy='noload'
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    currentValue: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))


class Message(Base):
    __tablename__ = 'Message'

    id: Mapped[str] = mapped_column(String(), primary_key=True)  # Discord Snowflake
    hubId: Mapped[str] = mapped_column(ForeignKey('Hub.id', ondelete='CASCADE'))
    content: Mapped[str]
    imageUrl: Mapped[Optional[str]]
    channelId: Mapped[str]
    guildId: Mapped[str]
    authorId: Mapped[str]
    createdAt: Mapped[datetime] = mapped_column(DateTime())
    reactions: Mapped[Optional[dict]] = mapped_column(JSON)
    referredMessageId: Mapped[Optional[str]] = mapped_column(ForeignKey('Message.id'))

    # Relationships
    hub: Mapped['Hub'] = relationship('Hub', back_populates='messages', lazy='noload', init=False)

    reports: Mapped[list['Report']] = relationship(
        'Report',
        back_populates='message',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    broadcasts: Mapped[list['Broadcast']] = relationship(
        'Broadcast',
        back_populates='message',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )

    referredTo: Mapped[Optional['Message']] = relationship(
        'Message',
        remote_side=[id],
        back_populates='referredBy',
        lazy='noload',
        foreign_keys=[referredMessageId],
        init=False,
    )
    referredBy: Mapped[list['Message']] = relationship(
        'Message',
        back_populates='referredTo',
        lazy='noload',
        foreign_keys=[referredMessageId],
        init=False,
    )

    __table_args__ = (
        Index('Message_hubId_idx', 'hubId'),
        Index('Message_authorId_idx', 'authorId'),
        Index('Message_guildId_idx', 'guildId'),
        Index('Message_referredMessageId_idx', 'referredMessageId'),
        Index('Message_createdAt_idx', text('createdAt DESC')),
        # Composite indexes
        Index('Message_guildId_authorId_idx', 'guildId', 'authorId'),
        Index('Message_hubId_createdAt_idx', 'hubId', text('createdAt DESC')),
        Index('Message_guildId_hubId_idx', 'guildId', 'hubId'),
    )


class Broadcast(Base):
    __tablename__ = 'Broadcast'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True
    )  # Discord message ID of the broadcast message
    messageId: Mapped[str] = mapped_column(
        String(), ForeignKey('Message.id', ondelete='CASCADE'), nullable=False
    )
    channelId: Mapped[str] = mapped_column(String(), nullable=False)
    message: Mapped['Message'] = relationship(
        'Message', back_populates='broadcasts', lazy='noload', init=False
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())

    __table_args__ = (
        Index('Broadcast_messageId_idx', 'messageId'),
        Index('Broadcast_channelId_idx', 'channelId'),
        Index('Broadcast_createdAt_idx', text('createdAt DESC')),
        Index('Broadcast_messageId_channelId_idx', 'messageId', 'channelId'),
        Index(
            'Broadcast_id_messageId_channelId_createdAt_idx',
            'id',
            'messageId',
            'channelId',
            'createdAt',
        ),
    )


class Report(Base):
    __tablename__ = 'Report'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(size=10), init=False
    )  # Short, unique report ID
    hubId: Mapped[str] = mapped_column(
        String(), ForeignKey('Hub.id', ondelete='CASCADE'), nullable=False
    )
    reporterId: Mapped[str] = mapped_column(
        String(), ForeignKey('User.id'), nullable=False
    )  # User who submitted the report
    reportedUserId: Mapped[str] = mapped_column(
        String(), ForeignKey('User.id'), nullable=False
    )  # User being reported
    reportedServerId: Mapped[str] = mapped_column(
        String(), nullable=False
    )  # Server where the reported content originated
    messageId: Mapped[str] = mapped_column(
        String(), ForeignKey('Message.id')
    )  # ID of the reported message (if applicable)
    handledBy: Mapped[str] = mapped_column(
        String(), ForeignKey('User.id')
    )  # Moderator who handled the report
    handledAt: Mapped[datetime] = mapped_column(DateTime)  # When the report was handled
    reason: Mapped[str] = mapped_column(String(), nullable=False)  # Reason for the report

    hub: Mapped['Hub'] = relationship('Hub', back_populates='reports', lazy='noload')
    reporter: Mapped['User'] = relationship(
        'User',
        foreign_keys=[reporterId],
        back_populates='reportsSubmitted',
        lazy='noload',
    )
    reportedUser: Mapped['User'] = relationship(
        'User',
        foreign_keys=[reportedUserId],
        back_populates='reportsReceived',
        lazy='noload',
    )
    handler: Mapped['User'] = relationship(
        'User', foreign_keys=[handledBy], back_populates='reportsHandled', lazy='noload'
    )
    message: Mapped['Message'] = relationship('Message', back_populates='reports', lazy='noload')

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    status: Mapped[ReportStatus] = mapped_column(
        SQLEnum(ReportStatus, name='ReportStatus', create_type=False),
        default=ReportStatus.PENDING,
        server_default=ReportStatus.PENDING.value,
    )

    __table_args__ = (
        Index('Report_hubId_idx', 'hubId'),
        Index('Report_status_idx', 'status'),
        Index('Report_createdAt_idx', 'createdAt'),
        Index('Report_reporterId_idx', 'reporterId'),
        Index('Report_messageId_idx', 'messageId'),
        Index('Report_handledBy_idx', 'handledBy'),
        Index('Report_reportedUserId_idx', 'reportedUserId'),
    )


class Blacklist(Base):
    __tablename__ = 'Blacklist'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(size=10), init=False
    )
    userId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'), nullable=False)
    moderatorId: Mapped[str] = mapped_column(
        String(), ForeignKey('User.id'), nullable=False
    )  # Staff member who issued the blacklist
    reason: Mapped[str] = mapped_column(String(), nullable=False)  # Reason for the blacklist
    expiresAt: Mapped[datetime | None] = mapped_column(
        DateTime(),
        default=None,
        server_default=sql.null(),
    )  # When the blacklist expires (null for permanent blacklists)

    user: Mapped['User'] = relationship(
        'User',
        foreign_keys=[userId],
        back_populates='blacklists',
        lazy='noload',
        init=False,
    )
    moderator: Mapped['User'] = relationship(
        'User',
        foreign_keys=[moderatorId],
        back_populates='issuedBlacklists',
        lazy='noload',
        init=False,
    )

    createdAt: Mapped[datetime] = mapped_column(
        DateTime(),
        server_default=func.now(),
        default=None,
    )
    updatedAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(),
        server_default=func.now(),
        onupdate=func.now(),
        default=None,
    )
    type: Mapped[BlacklistType] = mapped_column(
        SQLEnum(BlacklistType, name='BlacklistType', create_type=False),
        default=BlacklistType.PERMANENT,
        server_default=BlacklistType.PERMANENT.value,
    )

    __table_args__ = (
        Index('Blacklist_userId_idx', 'userId'),
        Index('Blacklist_expiresAt_idx', 'expiresAt'),
        Index('Blacklist_createdAt_idx', 'createdAt'),
        # Index for user blacklist validation during broadcasting
        Index('Blacklist_userId_expiresAt_idx', 'userId', 'expiresAt'),
    )


class ServerBlacklist(Base):
    __tablename__ = 'ServerBlacklist'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(size=10), init=False
    )
    serverId: Mapped[str] = mapped_column(String(), ForeignKey('ServerData.id'), nullable=False)
    moderatorId: Mapped[str] = mapped_column(String(), ForeignKey('User.id'), nullable=False)
    reason: Mapped[str] = mapped_column(String(), nullable=False)
    duration: Mapped[int | None] = mapped_column(
        Integer(), default=None, server_default=sql.null()
    )  # Duration in milliseconds for temporary blacklists (null for permanent)
    expiresAt: Mapped[datetime | None] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )  # When the blacklist expires (null for permanent blacklists)

    server: Mapped['ServerData'] = relationship(
        'ServerData', back_populates='serverBlacklists', lazy='noload', init=False
    )
    moderator: Mapped['User'] = relationship(
        'User', back_populates='issuedServerBlacklists', lazy='noload', init=False
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), default=None)
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(),
        server_default=func.now(),
        onupdate=func.now(),
        default=None,
    )
    type: Mapped[BlacklistType] = mapped_column(
        SQLEnum(BlacklistType, name='BlacklistType', create_type=False),
        default=BlacklistType.PERMANENT,
        server_default=BlacklistType.PERMANENT.value,
    )

    __table_args__ = (
        Index('ServerBlacklist_serverId_idx', 'serverId'),
        Index('ServerBlacklist_expiresAt_idx', 'expiresAt'),
        Index('ServerBlacklist_createdAt_idx', 'createdAt'),
        # Index for server blacklist validation during broadcasting
        Index(
            'ServerBlacklist_serverId_expiresAt_idx',
            'serverId',
            'expiresAt',
        ),
    )


class LeaderboardEntry(Base):
    __tablename__ = 'LeaderboardEntry'

    id: Mapped[str] = mapped_column(
        String(), primary_key=True, default=lambda: generate(), init=False
    )
    userId: Mapped[str] = mapped_column(
        String(), ForeignKey('User.id', ondelete='CASCADE'), nullable=False
    )
    hubId: Mapped[str] = mapped_column(String(), ForeignKey('Hub.id', ondelete='CASCADE'))
    serverId: Mapped[str] = mapped_column(String(), ForeignKey('ServerData.id', ondelete='CASCADE'))
    period: Mapped[LeaderboardPeriod] = mapped_column(
        SQLEnum(LeaderboardPeriod, name='LeaderboardPeriod', create_type=False),
        nullable=False,
    )
    type: Mapped[LeaderboardType] = mapped_column(
        SQLEnum(LeaderboardType, name='LeaderboardType', create_type=False),
        nullable=False,
    )

    user: Mapped['User'] = relationship('User', back_populates='leaderboardEntries', lazy='noload')
    hub: Mapped['Hub'] = relationship('Hub', back_populates='leaderboardEntries', lazy='noload')
    server: Mapped['ServerData'] = relationship(
        'ServerData', back_populates='leaderboardEntries', lazy='noload'
    )

    lastActivityAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    messageCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    score: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    rank: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))

    __table_args__ = (
        Index('LeaderboardEntry_type_period_score_idx', 'type', 'period', 'score'),
        Index('LeaderboardEntry_userId_type_period_idx', 'userId', 'type', 'period'),
        Index('LeaderboardEntry_hubId_type_period_idx', 'hubId', 'type', 'period'),
        Index('LeaderboardEntry_serverId_type_period_idx', 'serverId', 'type', 'period'),
        Index('LeaderboardEntry_lastActivityAt_idx', 'lastActivityAt'),
    )


class User(Base):
    __tablename__ = 'User'

    # Primary fields
    id: Mapped[str] = mapped_column(String(), primary_key=True)
    name: Mapped[Optional[str]] = mapped_column(String())
    image: Mapped[Optional[str]] = mapped_column(String())

    # Relations - ALL with init=False to exclude from constructor
    ownedHubs: Mapped[list['Hub']] = relationship(
        'Hub', back_populates='owner', lazy='noload', init=False
    )
    appeals: Mapped[list['Appeal']] = relationship(
        'Appeal', back_populates='user', lazy='noload', init=False
    )
    infractions: Mapped[list['Infraction']] = relationship(
        'Infraction',
        foreign_keys='Infraction.userId',
        back_populates='user',
        lazy='noload',
        init=False,
    )
    issuedInfractions: Mapped[list['Infraction']] = relationship(
        'Infraction',
        foreign_keys='Infraction.moderatorId',
        back_populates='moderator',
        lazy='noload',
        init=False,
    )
    upvotedHubs: Mapped[list['HubUpvote']] = relationship(
        'HubUpvote', back_populates='user', lazy='noload', init=False
    )
    reputationLog: Mapped[list['ReputationLog']] = relationship(
        'ReputationLog', back_populates='receiver', lazy='noload', init=False
    )
    modPositions: Mapped[list['HubModerator']] = relationship(
        'HubModerator', back_populates='user', lazy='noload', init=False
    )
    reviews: Mapped[list['HubReview']] = relationship(
        'HubReview',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    blockWordsCreated: Mapped[list['BlockWord']] = relationship(
        'BlockWord', back_populates='creator', lazy='noload', init=False
    )
    antiSwearRulesCreated: Mapped[list['AntiSwearRule']] = relationship(
        'AntiSwearRule', back_populates='creator', lazy='noload', init=False
    )
    rulesAcceptances: Mapped[list['HubRulesAcceptance']] = relationship(
        'HubRulesAcceptance', back_populates='user', lazy='noload', init=False
    )
    ratingsMade: Mapped[list['CallRating']] = relationship(
        'CallRating',
        foreign_keys='CallRating.raterId',
        back_populates='rater',
        lazy='noload',
        init=False,
    )
    ratingsReceived: Mapped[list['CallRating']] = relationship(
        'CallRating',
        foreign_keys='CallRating.targetId',
        back_populates='target',
        lazy='noload',
        init=False,
    )
    accounts: Mapped[list['Account']] = relationship(
        'Account',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    sessions: Mapped[list['Session']] = relationship(
        'Session',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    reportsSubmitted: Mapped[list['Report']] = relationship(
        'Report',
        foreign_keys='Report.reporterId',
        back_populates='reporter',
        lazy='noload',
        init=False,
    )
    reportsReceived: Mapped[list['Report']] = relationship(
        'Report',
        foreign_keys='Report.reportedUserId',
        back_populates='reportedUser',
        lazy='noload',
        init=False,
    )
    reportsHandled: Mapped[list['Report']] = relationship(
        'Report',
        foreign_keys='Report.handledBy',
        back_populates='handler',
        lazy='noload',
        init=False,
    )
    achievements: Mapped[list['UserAchievement']] = relationship(
        'UserAchievement',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    achievementProgress: Mapped[list['UserAchievementProgress']] = relationship(
        'UserAchievementProgress',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    blacklists: Mapped[list['Blacklist']] = relationship(
        'Blacklist',
        foreign_keys='Blacklist.userId',
        back_populates='user',
        lazy='noload',
        init=False,
    )
    issuedBlacklists: Mapped[list['Blacklist']] = relationship(
        'Blacklist',
        foreign_keys='Blacklist.moderatorId',
        back_populates='moderator',
        lazy='noload',
        init=False,
    )
    issuedServerBlacklists: Mapped[list['ServerBlacklist']] = relationship(
        'ServerBlacklist', back_populates='moderator', lazy='noload', init=False
    )
    leaderboardEntries: Mapped[list['LeaderboardEntry']] = relationship(
        'LeaderboardEntry',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    donations: Mapped[list['Donation']] = relationship(
        'Donation', back_populates='discordUser', lazy='noload', init=False
    )
    donationTier: Mapped['DonationTierDefinition | None'] = relationship(
        'DonationTierDefinition', back_populates='users', lazy='noload', init=False
    )

    # Timestamps
    lastMessageAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(),
        server_default=func.now(),
        default=None,
    )
    inboxLastReadDate: Mapped[Optional[datetime]] = mapped_column(
        DateTime(),
        server_default=func.now(),
        default=None,
    )
    createdAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(),
        server_default=func.now(),
        default=None,
    )
    updatedAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(),
        server_default=func.now(),
        onupdate=func.now(),
        default=None,
    )

    # Foreign keys
    donationTierId: Mapped[Optional[str]] = mapped_column(
        String(),
        ForeignKey('DonationTierDefinition.id'),
        default=None,
        server_default=sql.null(),
    )
    tagId: Mapped[Optional[str]] = mapped_column(String(), default=None, server_default=sql.null())

    # Optional enum field
    activityLevel: Mapped[Optional[HubActivityLevel]] = mapped_column(
        SQLEnum(HubActivityLevel, name='HubActivityLevel', create_type=False),
        default=None,
        server_default=sql.null(),
    )  # user's preferred activity level

    # Activity tracking for recommendations
    donationExpiresAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )  # When the user's donation expires
    lastHubJoinAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )  # Last time the user joined a hub

    # nextauth
    email: Mapped[Optional[str]] = mapped_column(String(), default=None, server_default=sql.null())
    emailVerified: Mapped[Optional[datetime]] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )
    donationEmail: Mapped[Optional[str]] = mapped_column(
        String(), default=None, server_default=sql.null()
    )  # Email for donation matching

    # Boolean flags
    showBadges: Mapped[bool] = mapped_column(Boolean(), default=True, server_default=sql.true())
    mentionOnReply: Mapped[bool] = mapped_column(Boolean(), default=True, server_default=sql.true())
    isStaff: Mapped[bool] = mapped_column(Boolean(), default=False, server_default=sql.false())
    showNsfwHubs: Mapped[bool] = mapped_column(Boolean(), default=False, server_default=sql.false())

    # Numeric fields
    voteCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    reputation: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    messageCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    hubJoinCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    hubEngagementScore: Mapped[float] = mapped_column(
        Float, default=0.0, server_default=text('0.0')
    )  # Calculated engagement metric

    locale: Mapped[Optional[str]] = mapped_column(String(), default='en')
    lastVoted: Mapped[Optional[datetime]] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )

    badges: Mapped[list[Badges]] = mapped_column(
        ARRAY(SQLEnum(Badges, name='Badges', create_type=False)),
        default=list,
        server_default=text('\'{}\'::"Badges"[]'),
    )

    # Hub recommendation preferences
    preferredLanguages: Mapped[list[str]] = mapped_column(
        ARRAY(String), default=list, server_default=text("'{}'::text[]")
    )

    # Indexes
    __table_args__ = (
        Index('User_isStaff_idx', 'isStaff'),
        Index('User_reputation_idx', 'reputation'),
        Index('User_locale_idx', 'locale'),
        Index('User_email_idx', 'email'),
        Index('User_voteCount_idx', 'voteCount'),
        Index('User_lastVoted_idx', 'lastVoted'),
        Index('User_createdAt_idx', 'createdAt'),
    )
