from datetime import datetime
from typing import Optional, TYPE_CHECKING
from discord import Em<PERSON>, Guild, Member, User, Message as DiscordMessage
from sqlalchemy import select, and_, or_
from utils.constants import InterchatConstants
from utils.modules.core.db.models import (
    Blacklist,
    BlacklistType,
    Hub,
    HubModerator,
    Infraction,
    InfractionStatus,
    InfractionType,
    Role,
    Message,
    Broadcast,
    ServerBlacklist,
)
from utils.utils import duration_to_datetime

if TYPE_CHECKING:
    from utils.modules.core.db.database import Database
    from main import Bot

constants = InterchatConstants()


async def get_user_moderated_hubs(bot: 'Bot', user_id: str):
    """
    Get all hubs where the user has Moderator or Manager permissions.

    Args:
        bot: The bot instance for database access
        user_id: The Discord user ID to check permissions for

    Returns:
        List of Hub objects where the user has moderation permissions
    """
    async with bot.db.get_session() as session:
        stmt = (
            select(Hub)
            .join(HubModerator, Hub.id == HubModerator.hubId, isouter=True)
            .where(
                or_(
                    Hub.ownerId == user_id,
                    and_(
                        HubModerator.userId == user_id,
                        HubModerator.role.in_([Role.MODERATOR, Role.MANAGER]),
                    ),
                )
            )
        )
        result = await session.execute(stmt)
        return result.scalars().all()


async def fetch_original_message(bot: 'Bot', message_id: str) -> Optional[Message]:
    """
    Fetch original message, first directly then through broadcast relationship.

    This function implements the message priority logic:
    1. First, try to fetch the original message directly from the database
    2. If not found, fetch the broadcast record and get the original message through the relationship

    Args:
        bot: The bot instance for database access
        message_id: The Discord message ID to fetch

    Returns:
        Message object if found, None otherwise
    """
    async with bot.db.get_session() as session:
        # First try to fetch the message directly
        stmt = select(Message).where(Message.id == message_id)
        result = await session.execute(stmt)
        message = result.scalar_one_or_none()

        if message:
            return message

        # If not found, try to fetch through broadcast relationship
        stmt = (
            select(Message)
            .join(Broadcast, Message.id == Broadcast.messageId)
            .where(Broadcast.id == message_id)
        )
        result = await session.execute(stmt)
        return result.scalar_one_or_none()


async def create_infraction(
    db: 'Database',
    hub_id: str,
    mod_id: str,
    reason: str,
    infraction_type: InfractionType,
    user_id: Optional[str] = None,
    server_id: Optional[str] = None,
    server_name: Optional[str] = None,
    duration: Optional[int] = None,
):
    """Create a new infraction record."""
    async with db.get_session() as session:
        infraction = Infraction(
            hubId=hub_id,
            moderatorId=mod_id,
            userId=user_id,
            serverId=server_id,
            serverName=server_name,
            reason=reason,
            type=infraction_type,
            status=InfractionStatus.ACTIVE,
            notified=False,
            expiresAt=duration_to_datetime(duration),
            createdAt=datetime.now(),
            updatedAt=datetime.now(),
        )

        session.add(infraction)
        await session.commit()
        return infraction


async def revoke_infraction(
    db: 'Database',
    infraction_id: str,
    status: InfractionStatus = InfractionStatus.REVOKED,
):
    """Revoke an infraction."""
    async with db.get_session() as session:
        stmt = select(Infraction).where(Infraction.id == infraction_id)
        result = await session.execute(stmt)
        infraction = result.scalar_one_or_none()

        if infraction:
            infraction.status = status
            await session.commit()


async def ban_user(
    bot: 'Bot',
    user_id: str,
    hub_id: str,
    mod_id: str,
    reason: str,
    duration: Optional[int] = None,
):
    """Ban a user from a hub."""
    return await create_infraction(
        db=bot.db,
        hub_id=hub_id,
        mod_id=mod_id,
        user_id=user_id,
        reason=reason,
        infraction_type=InfractionType.BAN,
        duration=duration,
    )


async def unban_user(bot: 'Bot', user_id: str, hub_id: str):
    """Unban a user from a hub."""
    async with bot.db.get_session() as session:
        stmt = select(Infraction).where(
            and_(
                Infraction.userId == user_id,
                Infraction.hubId == hub_id,
                Infraction.type == InfractionType.BAN,
                Infraction.status == InfractionStatus.ACTIVE,
            )
        )
        result = await session.execute(stmt)
        infraction = result.scalar_one_or_none()

        if infraction:
            await revoke_infraction(bot.db, infraction.id)


async def warn_user(db: 'Database', user_id: str, hub_id: str, mod_id: str, reason: str):
    """Warn a user in a hub."""
    return await create_infraction(
        db=db,
        hub_id=hub_id,
        mod_id=mod_id,
        user_id=user_id,
        reason=reason,
        infraction_type=InfractionType.WARNING,
        duration=30 * 24 * 60 * 60 * 1000,  # 30 days in milliseconds
    )


async def ban_server(
    bot: 'Bot',
    server_id: str,
    hub_id: str,
    mod_id: str,
    duration: Optional[int] = None,
    reason: str = 'No reason provided.',
):
    """Ban a server from a hub."""
    return await create_infraction(
        db=bot.db,
        hub_id=hub_id,
        mod_id=mod_id,
        server_id=server_id,
        reason=reason,
        infraction_type=InfractionType.BAN,
        duration=duration,
    )


async def unban_server(db: 'Database', server_id: str, hub_id: str):
    """Unban a server from a hub."""
    async with db.get_session() as session:
        stmt = select(Infraction).where(
            and_(
                Infraction.serverId == server_id,
                Infraction.hubId == hub_id,
                Infraction.type == InfractionType.BAN,
                Infraction.status == InfractionStatus.ACTIVE,
                Infraction.expiresAt == None,
            )
        )
        result = await session.execute(stmt)
        infraction = result.scalar_one_or_none()

        if infraction:
            await revoke_infraction(db, infraction.id)


async def blacklist_user(
    db: 'Database',
    user_id: str,
    mod_id: str,
    reason: str,
    duration: Optional[int] = None,
):
    """Blacklist a user from the interchat."""
    async with db.get_session() as session:
        stmt = Blacklist(
            userId=user_id,
            reason=reason,
            expiresAt=duration_to_datetime(duration),
            type=(BlacklistType.PERMANENT if duration is None else BlacklistType.TEMPORARY),
            moderatorId=mod_id,
        )
        session.add(stmt)
        await session.commit()


async def unblacklist_user(db: 'Database', user_id: str):
    """Unblacklist a user from interchat.

    Returns:
        True if the user was blacklisted, False otherwise.
    """
    async with db.get_session() as session:
        stmt = select(Blacklist).where(
            and_(
                Blacklist.userId == user_id,
                Blacklist.type == BlacklistType.PERMANENT,
            )
        )
        result = await session.execute(stmt)
        blacklist = result.scalar_one_or_none()

        if blacklist:
            await session.delete(blacklist)
            await session.commit()
            return True
    return False


async def blacklist_server(
    db: 'Database',
    server_id: str,
    mod_id: str,
    reason: str,
    duration: Optional[int] = None,
):
    """Blacklist a server from interchat."""
    async with db.get_session() as session:
        stmt = ServerBlacklist(
            serverId=server_id,
            reason=reason,
            expiresAt=duration_to_datetime(duration),
            type=(BlacklistType.PERMANENT if duration is None else BlacklistType.TEMPORARY),
            moderatorId=mod_id,
        )
        session.add(stmt)
        await session.commit()
        return stmt


async def unblacklist_server(db: 'Database', server_id: str):
    """Unblacklist a server from interchat.

    Returns:
        True if the server was blacklisted, False otherwise.
    """
    async with db.get_session() as session:
        stmt = select(ServerBlacklist).where(
            and_(
                ServerBlacklist.serverId == server_id,
                ServerBlacklist.type == BlacklistType.PERMANENT,
            )
        )
        result = await session.execute(stmt)
        blacklist = result.scalar_one_or_none()

        if blacklist:
            await session.delete(blacklist)
            await session.commit()
            return True
    return False


def mod_panel_embed(
    bot: 'Bot',
    hub: Hub,
    target_user: Optional[User | Member],
    target_server: Optional[Guild],
    target_message: Optional[DiscordMessage],
    user_reputation: Optional[int],
    user_infractions: Optional[int],
    server_infractions: Optional[int],
    _locale: str,
) -> Embed:
    """Create the embed for the mod panel."""
    emotes = bot.emotes
    message_field_str = (
        f'> **[Message Content]({target_message.jump_url})** {target_message.content.replace("`", "")[:50]}\n'
        if target_message
        else ''
    )
    server_str = (
        f'> **Server:** {target_server.name} (`{target_server.id}`)\n' if target_server else ''
    )

    embed = Embed(
        title='Moderation Panel',
        description=' ',
        color=constants.color(),
    )

    embed.add_field(
        name=f'{emotes.chat_icon} Context',
        value=f'{message_field_str}{server_str}> **Hub:** {hub.name}',
        inline=False,
    )

    user_info_str = (
        f'> **User:** {target_user.name} (`{target_user.id}`)\n'
        f'> **User Infractions:** {user_infractions}\n'
        f'> **Reputation:** {user_reputation}\n'
        if target_user
        else ''
    )
    server_info_str = (
        f'> **Server:** {target_server.name} (`{target_server.id}`)\n'
        f'> **Owner:** <@{target_server.owner_id}>\n'
        f'> **Server Infractions:** {server_infractions}\n'
        if target_server
        else ''
    )
    embed.add_field(
        name=f'{emotes.info_icon} Target Information',
        value=f'{user_info_str}{server_info_str}',
        inline=False,
    )

    if target_user:
        embed.set_author(name=target_user.name, icon_url=target_user.display_avatar.url)
    elif target_server:
        embed.set_author(
            name=target_server.name,
            icon_url=target_server.icon.url if target_server.icon else None,
        )

    return embed
