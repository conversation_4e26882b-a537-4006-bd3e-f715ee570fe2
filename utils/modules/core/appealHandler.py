import discord
from discord.ui import View, button, Button

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot


class AppealView(View):
    def __init__(self, bot, user, constants):
        self.bot: Bot = bot
        self.user = user
        self.constants = constants

    async def setup_button(self):
        self.appeal_callback.emoji = self.bot.emotes.hammer_icon
        self.appeal_callback.custom_id = f'appealButton{self.ban.id}'

    @button(label='Submit appeal', style=discord.ButtonStyle.gray)
    async def appeal_callback(self, interaction: discord.Interaction, button: Button): ...
