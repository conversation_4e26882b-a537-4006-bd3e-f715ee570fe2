from discord.ext import commands


class <PERSON>actionCheck(commands.CommandError):
    def __init__(self, message='You may not use this interaction, as you did not invoke it.'):
        self.message = message
        super().__init__(message)


class RateLimited(commands.CommandError):
    def __init__(self, message='You are being rate limited. Take a chill pill.'):
        self.message = message
        super().__init__(message)


class WebhookRateLimit(commands.CommandError):
    def __init__(self, message='You have reached the webhook creation rate limit'):
        self.message = message
        super().__init__(message)


class InvalidInput(commands.CommandError):
    def __init__(self, message='You have not provided a valid input.'):
        self.message = message
        super().__init__(message)


class InvalidInvite(commands.CommandError):
    def __init__(self, message='This invite is invalid, or expired.'):
        self.message = message
        super().__init__(message)


class WebhookError(commands.CommandError):
    def __init__(self, message='Failed to create webhook.'):
        self.mesage = message
        super().__init__(message)


class NotConnected(commands.CommandError):
    def __init__(self, message='I could not find a hub connected in this channel.'):
        self.message = message
        super().__init__(message)


class NoInteraction(commands.CommandError):
    def __init__(self, message='This command only supports slash commands.'):
        self.message = message
        super().__init__(message)
