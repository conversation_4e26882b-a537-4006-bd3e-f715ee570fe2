from zuid import ZUID

import discord
from discord.ui import View, button, Button, TextInput, Select, UserSelect

from sqlalchemy import select
from sqlalchemy.orm import selectinload

from utils.modules.core.checks import interaction_check
from utils.modules.core.db.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON>n<PERSON><PERSON>, HubModerator, Role

from utils.modules.errors.errorHandler import error_handler
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.hub.utils import get_user_permission
from utils.utils import (
    duration_to_datetime,
    parse_duration,
)
from utils.modules.ui.CustomModal import CustomModal

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.core.db.database import Database

# Constants
ERROR_TITLE = 'Error!'
NOT_IMPLEMENTED_MSG = 'Not implemented yet'


async def get_hub_with_moderators(hub_id: str, db: 'Database'):
    async with db.get_session() as session:
        stmt = (
            select(Hub)
            .where(Hub.id == hub_id)
            .options(selectinload(Hub.moderators).selectinload(HubModerator.user))
        )
        hub = (await session.execute(stmt)).scalar()

    return hub


class HubPermissions(View):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        constants,
        options: list[discord.SelectOption] | None,
        permission: HubPermissionLevel,
    ):
        super().__init__()
        self.bot = bot
        self.user = user
        self.hub = hub
        self.constants = constants
        self.options = options
        self.permission = permission
        self.selected_user = None
        self.role = None

    async def setup_role_select(self):
        hub = await get_hub_with_moderators(self.hub.id, self.bot.db)

        if not hub:
            return

        user_id = str(self.selected_user.id)
        user_level = get_user_permission(hub, user_id)
        invoker_level = self.permission

        options = []

        if user_level > HubPermissionLevel.NONE:
            options.append(
                discord.SelectOption(
                    emoji=self.bot.emotes.delete_icon, label='Remove', value='remove'
                )
            )

        if invoker_level >= HubPermissionLevel.OWNER:
            if user_level == HubPermissionLevel.NONE:
                options.extend(
                    [
                        discord.SelectOption(
                            emoji=self.bot.emotes.hammer_icon,
                            label='Moderator',
                            description='Manage messages, moderate users, handle reports & appeals',
                            value='moderator',
                        ),
                        discord.SelectOption(
                            emoji=self.bot.emotes.gear_icon,
                            label='Manager',
                            description='Manage hub settings plus everything that moderators can do',
                            value='manager',
                        ),
                    ]
                )
            elif user_level == HubPermissionLevel.MODERATOR:
                options.append(
                    discord.SelectOption(
                        emoji=self.bot.emotes.gear_icon,
                        label='Manager',
                        description='Manage hub settings',
                        value='manager',
                    )
                )
            elif user_level == HubPermissionLevel.MANAGER:
                options.append(
                    discord.SelectOption(
                        emoji=self.bot.emotes.hammer_icon,
                        label='Moderator',
                        description='Manage messages, moderate users, handle reports & appeals',
                        value='moderator',
                    )
                )

        elif invoker_level >= HubPermissionLevel.MANAGER:
            if user_level == HubPermissionLevel.NONE:
                options.append(
                    discord.SelectOption(
                        emoji=self.bot.emotes.hammer_icon,
                        label='Moderator',
                        value='moderator',
                    )
                )

        if options:
            self.role_on_submit.options = options
            self.role_on_submit.disabled = False
            self.role_on_submit.placeholder = 'Choose an action'
        else:
            self.role_on_submit.options = [
                discord.SelectOption(label='No actions available', value='none')
            ]
            self.role_on_submit.disabled = True
            self.role_on_submit.placeholder = 'No actions available'

    @discord.ui.select(
        placeholder='Add or manage member',
        cls=discord.ui.UserSelect,
        max_values=1,
        min_values=1,
    )
    async def user_on_submit(
        self, interaction: discord.Interaction['Bot'], select: discord.ui.UserSelect
    ):
        await interaction.response.defer()
        await interaction_check(interaction, interaction.user, self.user)

        if select.values[0] == interaction.user:
            embed = discord.Embed(
                title=f'{self.bot.emotes.x_icon} Nice Try! 😄',
                description="You can't change your own permissions silly! Pick someone else to manage.",
                color=discord.Color.red(),
            )
            return await interaction.followup.send(embed=embed, ephemeral=True)

        self.selected_user = select.values[0]
        await self.setup_role_select()
        await interaction.edit_original_response(view=self)

    @discord.ui.select(
        placeholder='Assign a role',
        options=[discord.SelectOption(label='Loading...', value='loading')],
        disabled=True,
        max_values=1,
        min_values=1,
    )
    async def role_on_submit(self, interaction: discord.Interaction['Bot'], select: Select):
        await interaction.response.defer()
        await interaction_check(interaction, interaction.user, self.user)
        self.role = select.values[0]

        for option in select.options:
            if option.value == select.values[0]:
                option.default = True
            else:
                option.default = False

        self.confirm_callback.disabled = False
        await interaction.edit_original_response(view=self)

    @discord.ui.button(label='Confirm', style=discord.ButtonStyle.grey, disabled=True)
    async def confirm_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()
        await interaction_check(interaction, interaction.user, self.user)

        if not self.selected_user or not self.role:
            await interaction.followup.send('Please select a user and role first.', ephemeral=True)
            return

        async with self.bot.db.get_session() as session:
            # Get the hub with moderators
            hub = await get_hub_with_moderators(self.hub.id, self.bot.db)

            if not hub:
                await interaction.followup.send(
                    'Hub not found. Please try again later.', ephemeral=True
                )
                return

            user_id = str(self.selected_user.id)

            # Check if user is already a moderator
            existing_moderator = None
            for mod in hub.moderators:
                if mod.userId == user_id:
                    existing_moderator = mod
                    break

            if self.role == 'remove':
                if existing_moderator:
                    # Remove the moderator
                    await session.delete(existing_moderator)
                    await session.commit()

                    embed = discord.Embed(
                        title=f'{self.bot.emotes.tick_icon} Team Member Removed',
                        description=f'**{self.selected_user.name}** is no longer part of your hub staff.',
                        color=discord.Color.green(),
                    )
                    embed.set_footer(text=f'Hub: {self.hub.name}')
                    await interaction.followup.send(embed=embed, ephemeral=True)
                else:
                    embed = discord.Embed(
                        title=f'{self.bot.emotes.x_icon} Oops!',
                        description=f"**{self.selected_user.name}** isn't part of your hub staff team yet. Want to add them?",
                        color=discord.Color.red(),
                    )
                    await interaction.followup.send(embed=embed, ephemeral=True)

            elif self.role in ['moderator', 'manager']:
                role_value = Role.MODERATOR if self.role == 'moderator' else Role.MANAGER
                role_name = 'Moderator' if self.role == 'moderator' else 'Manager'
                if existing_moderator:
                    # Update existing moderator role
                    existing_moderator.role = role_value
                    await session.commit()

                    embed = discord.Embed(
                        title=f'{self.bot.emotes.tick_icon} Promotion!',
                        description=f'**{self.selected_user.name}** has been promoted to **{role_name}**!',
                        color=discord.Color.green(),
                    )
                else:
                    # Add new moderator
                    new_moderator = HubModerator(hubId=self.hub.id, userId=user_id, role=role_value)
                    session.add(new_moderator)
                    await session.commit()

                    embed = discord.Embed(
                        title=f'{self.bot.emotes.tick_icon} New Team Member!',
                        description=f'**{self.selected_user.name}** is now a **{role_name}**! Your hub just got even more awesome!',
                        color=discord.Color.green(),
                    )

                embed.set_footer(text=f'Hub: {self.hub.name}')
                await interaction.followup.send(embed=embed, ephemeral=True)


class TransferConfirmationView(View):
    def __init__(
        self,
        bot,
        user: discord.User | discord.Member,
        hub: Hub,
        constants,
        selected: discord.User | discord.Member,
    ):
        super().__init__(timeout=20)
        self.bot = bot
        self.user = user
        self.hub = hub
        self.constants = constants
        self.selected_user = selected

    @button(label='Confirm', style=discord.ButtonStyle.red)
    async def confirm_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        async with self.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == self.hub.id)
            result = (await session.execute(stmt)).scalar()

            result.ownerId = str(self.selected_user.id)
            await session.commit()

        embed = discord.Embed(
            title='Success!',
            description=f'Ownership of **{self.hub.name}** has succesfully been transferred to {self.selected_user.mention}.',
            color=self.constants.color(),
        )
        view = None
        await interaction.edit_original_response(embed=embed, view=view)

    @button(label='Cancel', style=discord.ButtonStyle.grey)
    async def cancel_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        embed = discord.Embed(
            title='Cancelled',
            description='Operation cancelled. I have not transferred hub ownership.',
            color=self.constants.color(),
        )
        view = None
        await interaction.edit_original_response(embed=embed, view=view)


class OwnershipTransfer(View):
    def __init__(self, bot, user: discord.User | discord.Member, hub: Hub, constants):
        super().__init__(timeout=90)
        self.bot = bot
        self.user = user
        self.hub = hub
        self.constants = constants
        self.selected_user: discord.User | discord.Member

    @discord.ui.select(
        placeholder='Select a user', max_values=1, min_values=1, cls=UserSelect
    )
    async def on_submit(self, interaction: discord.Interaction['Bot'], select: UserSelect):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        self.selected_user = select.values[0]
        self.confirm_callback.disabled = False

        await interaction.edit_original_response(view=self)

    @button(label='Confirm', style=discord.ButtonStyle.red, disabled=True)
    async def confirm_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        embed = discord.Embed(
            title='Warning',
            description=f'Are you sure you would like to **permenantly** transfer hub ownership to {self.selected_user.mention}?',
            color=discord.Color.red(),
        )
        view = TransferConfirmationView(
            self.bot, self.user, self.hub, self.constants, self.selected_user
        )

        await interaction.edit_original_response(embed=embed, view=view)


class ConfigurationView(View):  # TODO: Ensure there is a return button uwu
    def __init__(
        self,
        bot: 'Bot',
        user,
        hub: Hub,
        constants,
        options,
        permission: HubPermissionLevel,
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.constants = constants
        self.hub = hub
        self.user = user
        self.options = options
        self.permission = permission
        self.message: discord.Message

        self.setup_select()

    def setup_select(self):
        self.on_submit.options = self.options

    @discord.ui.select(
        placeholder='Select an option',
        options=[
            discord.SelectOption(label='Loading...', description='Please wait.', value='NONE')
        ],
        max_values=1,
        min_values=1,
    )
    async def on_submit(self, interaction: discord.Interaction['Bot'], selected_item: Select):
        await interaction.response.defer()
        await interaction_check(interaction, interaction.user, self.user)

        match selected_item.values[0]:
            case 'hperm':
                await self._handle_hub_permissions(interaction)
            case 'hsannounce':
                await interaction.response.send_message('Still a WIP, coming soon!')
            case 'hgeneral':
                await interaction.response.send_message('Still a WIP, coming soon!')
            case 'hmodules':
                await interaction.response.send_message('Still a WIP, coming soon!')
            case 'transfer':
                await self._handle_ownership_transfer(interaction)
            case _:
                await interaction.followup.send(NOT_IMPLEMENTED_MSG, ephemeral=True)

    async def _handle_hub_permissions(self, interaction: discord.Interaction['Bot']):
        """Handle hub permissions configuration."""
        db_hub = await get_hub_with_moderators(self.hub.id, self.bot.db)

        if not db_hub:
            await interaction.followup.send('Hub not found.', ephemeral=True)
            return

        # Create the embed showing current hub moderators
        embed = discord.Embed(
            description=f'### {self.bot.emotes.person_icon} Hub Team Management\nManage your team and add new members.',
            color=self.constants.color(),
        )

        # Add owner field
        try:
            owner = await self.bot.fetch_user(int(db_hub.ownerId))
        except discord.NotFound:
            owner = None

        owner_str = 'Unknown' if owner is None else f'{owner.name} (`{owner.id}`)'

        embed.add_field(
            name=f'{self.bot.emotes.owner} Hub Owner',
            value=f'**{owner_str}**\n-# *The mastermind behind this hub! ✨*',
            inline=False,
        )

        # Add moderators if any exist
        if db_hub.moderators:
            managers = []
            moderators = []

            for mod in db_hub.moderators:
                user_info = f'**{mod.user.name}** (`{mod.userId}`)'

                if mod.role == Role.MANAGER:
                    managers.append(user_info)
                else:
                    moderators.append(user_info)

            if managers:
                embed.add_field(
                    name=f'{self.bot.emotes.gear_icon} Hub Managers ({len(managers)})',
                    value='\n\n'.join(managers),
                    inline=True,
                )

            if moderators:
                embed.add_field(
                    name=f'{self.bot.emotes.hammer_icon} Moderators ({len(moderators)})',
                    value='\n\n'.join(moderators),
                    inline=True,
                )

        embed.set_footer(text=f'Hub: {self.hub.name} - Total Staff: {1 + len(db_hub.moderators)}')

        view = HubPermissions(
            self.bot,
            interaction.user,
            self.hub,
            self.constants,
            None,
            self.permission,
        )
        await interaction.followup.send(embed=embed, view=view, ephemeral=True)

    async def _handle_ownership_transfer(self, interaction: discord.Interaction['Bot']):
        await interaction_check(interaction, self.user, interaction.user)

        embed = discord.Embed(
            title='Ownership Transfer',
            description="Transferring hub ownership is final and **cannot** be undone, not by staff, not by developers. Make sure you're absolutely sure before proceeding.",
            color=discord.Color.red(),
        )
        view = OwnershipTransfer(self.bot, self.user, self.hub, self.constants)
        await self.message.edit(embed=embed, view=view)


class InviteView(View):
    def __init__(self, bot, constants, user, hub):
        super().__init__(timeout=180)
        self.bot = bot
        self.constants = constants
        self.user = user
        self.hub = hub

    def setup_button(self):
        self.create_invite_callback.emoji = self.bot.emotes.link_icon

    @button(emoji='➕', label='Create', style=discord.ButtonStyle.grey)
    async def create_invite_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction_check(interaction, self.user, interaction.user)

        modal = CustomModal(
            'Input Required',
            [
                (
                    'customcode',
                    TextInput(
                        label='Custom Code',
                        placeholder='abc123',
                        required=False,
                        max_length=40,
                    ),
                ),
                (
                    'uses',
                    TextInput(
                        label='Uses',
                        placeholder='Leave me blank for infinte',
                        required=False,
                    ),
                ),
                (
                    'expire',
                    TextInput(label='Expiry', placeholder='1 week', required=True),
                ),
            ],
        )

        await interaction.response.send_modal(modal)
        await modal.wait()

        generator = ZUID(
            prefix=(f'{modal.customcode.value}_' if modal.customcode.value is not None else None),
            length=10,
        )

        if modal.uses.value is None:
            uses = 0
        else:
            try:
                uses = int(modal.uses.value)
            except Exception as e:
                err = await error_handler(interaction.client, interaction, e)
                if err:
                    raise err

        code = generator()

        invite = HubInvite(
            code=code,
            expires=duration_to_datetime(parse_duration(modal.expire.value)),
            maxUses=uses,
            hubId=self.hub.id,
        )

        async with self.bot.db.get_session() as session:
            session.add(invite)
            await session.commit()

        embed = discord.Embed(
            title='Generated!',
            description=f'{self.bot.emotes.tick_icon} `{code}` can now be used {uses} time(s) to join your hub. Share it with your friends!',
            color=discord.Color.green(),
        )
        embed.set_footer(text=f'Hub: {self.hub.name}')
        await interaction.followup.send(embed=embed, ephemeral=True)
