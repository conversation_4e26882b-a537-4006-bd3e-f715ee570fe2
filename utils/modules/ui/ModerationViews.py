from collections.abc import Sequence
import discord
from discord.ui import View, button, But<PERSON>, Select
from typing import Optional, TYPE_CHECKING

from utils.modules.core.db.models import Hub
from utils.modules.core.checks import interaction_check, is_interchat_staff_direct
from utils.modules.core.moderation import mod_panel_embed

if TYPE_CHECKING:
    from main import Bot


class HubSelectionView(View):
    """View for selecting a hub when no message is provided."""

    def __init__(
        self,
        bot: 'Bo<PERSON>',
        moderator: discord.User | discord.Member,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
        user_hubs: Sequence[Hub],
        constants,
        locale: str,
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.moderator = moderator
        self.target_user = target_user
        self.target_server = target_server
        self.target_message = target_message
        self.user_hubs = user_hubs
        self.constants = constants
        self.locale = locale

        # Add hub selection dropdown
        self.add_item(HubSelectDropdown(self.user_hubs, self))

    async def update_with_hub(self, interaction: discord.Interaction, selected_hub: Hub):
        """Update the view after a hub is selected - show the mod panel."""
        # Create the main mod panel
        view = ModPanelView(
            self.bot,
            self.moderator,
            self.target_user,
            self.target_server,
            self.target_message,
            selected_hub,
            self.constants,
            self.locale,
        )

        # Create embed
        embed = mod_panel_embed(
            self.bot,
            selected_hub,
            self.target_user,
            self.target_server,
            self.target_message,
            user_reputation=69,
            user_infractions=69,
            server_infractions=69,
            _locale=self.locale,
        )

        await interaction.response.edit_message(embed=embed, view=view)


class HubSelectDropdown(Select):
    """Dropdown for selecting a hub."""

    def __init__(self, hubs: Sequence[Hub], parent_view: HubSelectionView):
        options = []
        for hub in hubs[:25]:  # Discord limit of 25 options
            options.append(
                discord.SelectOption(
                    label=hub.name,
                    value=hub.id,
                    description=(
                        hub.shortDescription[:100] if hub.shortDescription else 'No description'
                    ),
                )
            )

        super().__init__(
            placeholder='Select a hub to moderate...',
            options=options,
            min_values=1,
            max_values=1,
        )
        self.hubs = hubs
        self.parent_view = parent_view

    async def callback(self, interaction: discord.Interaction):
        """Handle hub selection."""
        if not await interaction_check(interaction, self.parent_view.moderator, interaction.user):
            return

        selected_hub_id = self.values[0]
        selected_hub = next((hub for hub in self.hubs if hub.id == selected_hub_id), None)

        if selected_hub:
            await self.parent_view.update_with_hub(interaction, selected_hub)


class ModPanelView(View):
    """Main moderation panel with action selection."""

    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
        selected_hub: Hub,
        constants,
        locale: str,
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.moderator = moderator
        self.target_user = target_user
        self.target_server = target_server
        self.target_message = target_message
        self.selected_hub = selected_hub
        self.constants = constants
        self.locale = locale

        # Add the action select menu
        self.add_item(ModActionSelect(self))

    async def handle_action_selection(self, interaction: discord.Interaction, action: str):
        """Handle when a moderation action is selected."""
        # Check if we need target selection (both user and server available)
        needs_target_selection = (
            self.target_user is not None
            and self.target_server is not None
            and action != 'delete'  # Delete doesn't need target selection
        )

        if needs_target_selection:
            # Type guard: At this point we know both are not None
            assert self.target_user is not None
            assert self.target_server is not None

            # Show target selection view
            view = TargetSelectionView(
                self.bot,
                self.moderator,
                self.target_user,  # Now guaranteed not None
                self.target_server,  # Now guaranteed not None
                self.target_message,
                self.selected_hub,
                action,
                self.constants,
                self.locale,
            )

            embed = discord.Embed(
                title='Target Selection',
                description=f'Who do you want to {action}?',
                color=discord.Color.blue(),
            )
            embed.add_field(
                name='User',
                value=f'{self.target_user.mention} (`{self.target_user.id}`)',
                inline=True,
            )
            embed.add_field(
                name='Server',
                value=f'**{self.target_server.name}** (`{self.target_server.id}`)',
                inline=True,
            )

            await interaction.response.send_message(embed=embed, view=view, ephemeral=True)
        else:
            # Execute action directly
            await self._execute_action(interaction, action)

    async def _execute_action(
        self,
        interaction: discord.Interaction,
        action: str,
        target_type: Optional[str] = None,
    ):
        """Execute the selected moderation action."""
        # Import here to avoid circular imports
        from cogs.modules.moderation import ModerationActionHandler

        handler = ModerationActionHandler(
            self.bot,
            self.moderator,
            self.selected_hub,
            self.constants,
            self.locale,
        )

        # Determine targets based on action and target_type
        if action == 'delete' and self.target_message:
            await handler.handle_delete_message(interaction, self.target_message)
        elif target_type == 'user' or (
            not target_type and self.target_user and not self.target_server
        ):
            if self.target_user:
                await handler.handle_user_action(interaction, action, self.target_user)
            else:
                await interaction.response.send_message(
                    f'{self.bot.emotes.x_icon} No user target available.',
                    ephemeral=True,
                )
        elif target_type == 'server' or (
            not target_type and self.target_server and not self.target_user
        ):
            if self.target_server:
                await handler.handle_server_action(interaction, action, self.target_server)
            else:
                await interaction.response.send_message(
                    f'{self.bot.emotes.x_icon} No server target available.',
                    ephemeral=True,
                )
        else:
            await interaction.response.send_message(
                f'{self.bot.emotes.x_icon} Invalid target configuration.',
                ephemeral=True,
            )


class ModActionSelect(Select):
    """Select dropdown for moderation actions."""

    def __init__(self, parent_view: ModPanelView):
        self.parent_view = parent_view

        # Build options based on context
        options = []

        # Add delete option if message is provided
        emotes = parent_view.bot.emotes
        if parent_view.target_message:
            options.append(
                discord.SelectOption(
                    emoji=emotes.delete_icon,
                    label='Delete',
                    description='Delete the selected message across all connected hubs',
                    value='delete',
                )
            )

        # Standard moderation options
        options.extend(
            [
                discord.SelectOption(
                    emoji=emotes.alert_icon,
                    label='Warn',
                    description='Issue a warning to the selected user',
                    value='warn',
                ),
                discord.SelectOption(
                    emoji=emotes.clock_icon,
                    label='Mute',
                    description='Mute a user from the specified hub',
                    value='mute',
                ),
                discord.SelectOption(
                    emoji=emotes.hammer_icon,
                    label='Ban',
                    description='Ban a user/server from the specified hub',
                    value='ban',
                ),
            ]
        )

        if is_interchat_staff_direct(parent_view.bot, parent_view.moderator):
            # Add blacklist option
            blacklist_option = discord.SelectOption(
                emoji=parent_view.bot.emotes.hammer_icon,
                label='Blacklist',
                description='Issue an interchat wide blacklist (user/server)',
                value='blacklist',
            )
            options.append(blacklist_option)

        super().__init__(
            placeholder='Select a moderation action...',
            options=options,
            min_values=1,
            max_values=1,
        )

    async def callback(self, interaction: discord.Interaction):
        """Handle action selection."""
        if not await interaction_check(interaction, self.parent_view.moderator, interaction.user):
            return

        action = self.values[0]

        # If they selected something other than blacklist, or they are staff, proceed
        if action != 'blacklist':
            await self.parent_view.handle_action_selection(interaction, action)
        else:
            # TODO: work on blacklist
            return


class TargetSelectionView(View):
    """View for selecting between user and server targets after action selection."""

    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: discord.User | discord.Member,
        target_server: discord.Guild,
        target_message: Optional[discord.Message],
        selected_hub: Hub,
        selected_action: str,
        constants,
        locale: str,
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.moderator = moderator
        self.target_user = target_user
        self.target_server = target_server
        self.target_message = target_message
        self.selected_hub = selected_hub
        self.selected_action = selected_action
        self.constants = constants
        self.locale = locale

    @button(label='Act on User', style=discord.ButtonStyle.primary, emoji='👤')
    async def select_user(self, interaction: discord.Interaction, button: Button):
        """Select user as the target."""
        if not await interaction_check(interaction, self.moderator, interaction.user):
            return

        # Import here to avoid circular imports
        from cogs.modules.moderation import ModerationActionHandler

        handler = ModerationActionHandler(
            self.bot,
            self.moderator,
            self.selected_hub,
            self.constants,
            self.locale,
        )

        await handler.handle_user_action(interaction, self.selected_action, self.target_user)

    @button(label='Act on Server', style=discord.ButtonStyle.secondary, emoji='🏢')
    async def select_server(self, interaction: discord.Interaction, button: Button):
        """Select server as the target."""
        if not await interaction_check(interaction, self.moderator, interaction.user):
            return

        # Import here to avoid circular imports
        from cogs.modules.moderation import ModerationActionHandler

        handler = ModerationActionHandler(
            self.bot,
            self.moderator,
            self.selected_hub,
            self.constants,
            self.locale,
        )

        await handler.handle_server_action(interaction, self.selected_action, self.target_server)
