from typing import TYPE_CHECKING
from discord import app_commands
import discord

from utils.modules.core.db.models import <PERSON><PERSON>, HubModerator

from sqlalchemy import select, or_
import json

if TYPE_CHECKING:
    from main import Bot


# Hubs : Moderator+
async def _hubm_autocomplete(
    interaction: discord.Interaction['Bot'], current: str
) -> list[app_commands.Choice[str]]:
    user_id = str(interaction.user.id)

    async with interaction.client.db.get_session() as session:
        stmt = select(Hub.name, Hub.id).where(
            or_(Hub.ownerId == user_id, Hub.moderators.any(HubModerator.id == user_id))
        )
        hubs = (await session.execute(stmt)).all()

    choices = []
    for hub_name, hub_id in hubs:
        if current.lower() in hub_name.lower():
            hub_data = json.dumps({'id': str(hub_id), 'name': str(hub_name)})
            choices.append(app_commands.Choice(name=hub_name, value=hub_data))

    return choices[:25]


def hubm_autocomplete(func):
    return app_commands.autocomplete(hub=_hubm_autocomplete)(func)


# Hub : Owner
async def _hubo_autocomplete(
    interaction: discord.Interaction['Bot'], current: str
) -> list[app_commands.Choice[str]]:
    user_id = str(interaction.user.id)

    async with interaction.client.db.get_session() as session:
        stmt = select(Hub.name, Hub.id).where(Hub.ownerId == user_id)
        hubs = (await session.execute(stmt)).all()

    choices = []
    for hub_name, hub_id in hubs:
        if current.lower() in hub_name.lower():
            hub_data = json.dumps({'id': str(hub_id), 'name': str(hub_name)})
            choices.append(app_commands.Choice(name=hub_name, value=hub_data))

    return choices[:25]


def hubo_autocomplete(func):
    return app_commands.autocomplete(hub=_hubo_autocomplete)(func)
