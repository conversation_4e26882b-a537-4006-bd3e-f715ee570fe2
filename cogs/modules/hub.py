from datetime import datetime
import json

import discord
from discord.ext import commands

from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

from utils.modules.core.db.models import Connection, Hub, HubInvite
from utils.modules.core.webhookCore import (
    get_or_create_webhook,
    get_and_cleanup_webhooks,
)
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.hub.utils import get_user_permission
from utils.modules.ui.AutoComplete import hubm_autocomplete, hubo_autocomplete
from utils.modules.errors.customDiscord import (
    InvalidInput,
    InvalidInvite,
    WebhookError,
    NotConnected,
)
from utils.constants import InterchatConstants
from utils.modules.ui.HubConfigViews import ConfigurationView, InviteView
from utils.utils import check_user

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from main import Bot


class Hubs(commands.Cog):
    def __init__(self, bot):
        self.bot: Bot = bot
        self.constants = InterchatConstants()

    @commands.hybrid_command(
        name='hubs', description='View InterChat hubs', extras={'category': 'Hubs'}
    )
    @check_user()
    async def hubs(self, ctx: commands.Context[commands.Bot]):
        async with self.bot.db.get_session() as session:
            hubs = (await session.execute(select(Hub))).scalars().all()
            if not hubs:
                await ctx.send(content='no hubs found')
                return

            content = ''
            for hub in hubs:
                content += f'{hub.name} ({hub.id}): {hub.shortDescription} | {hub.description}\n'
            await ctx.send(content=content)

    @commands.hybrid_group()
    async def hub(self, ctx: commands.Context[commands.Bot]):
        pass

    @hub.command(
        name='create',
        description='Create an InterChat hub',
        extras={'category': 'Hubs'},
    )
    @check_user()
    async def create_hub(
        self,
        ctx: commands.Context[commands.Bot],
        hub_name: str,
        short_description: str,
        description: str,
        icon_url: str,
    ):  # i'd do this interactivley tbh with a view
        async with self.bot.db.get_session() as session:
            hub = Hub(
                name=hub_name,
                shortDescription=short_description,
                description=description,
                ownerId=str(ctx.author.id),
                iconUrl=icon_url,
                rules=[],
                createdAt=datetime.now(),
                updatedAt=datetime.now(),
                lastActive=datetime.now(),
            )
            session.add(hub)
            await ctx.send(content='hub created')
            return

        await ctx.send(content='Failed to create hub. Please try again later.')

    @hub.command(
        name='configure',
        description='Configure your InterChat hub',
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    @check_user()
    async def configure(self, ctx: commands.Context[commands.Bot], hub):
        hub_obj = json.loads(hub)

        async with self.bot.db.get_session() as session:
            db_hub = await session.scalar(select(Hub).where(Hub.id == hub_obj['id']))

            if not db_hub:
                raise InvalidInput()

            user_level = get_user_permission(db_hub, str(ctx.author.id))
            options = []

            if user_level >= HubPermissionLevel.OWNER:
                options += [
                    discord.SelectOption(
                        emoji=self.bot.emotes.globe_icon,
                        label='Transfer Ownership',
                        description="Transfer your hub's ownership to another person",
                        value='transfer',
                    )
                ]

            if user_level >= HubPermissionLevel.MANAGER:
                options += [
                    discord.SelectOption(
                        emoji=self.bot.emotes.house_icon,
                        label='General Settings',
                        description="Customize your hub's appearance, rules, and more",
                        value='hgeneral',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.person_icon,
                        label='Team Management',
                        description="Manage your hub's team and staff permissions",
                        value='hperm',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.gear_icon,
                        label='Modules',
                        description='Enable or disable additional modules within your hub',
                        value='hmodules',
                    ),
                ]

            if user_level >= HubPermissionLevel.MODERATOR:
                options.append(
                    discord.SelectOption(
                        emoji=self.bot.emotes.chat_icon,
                        label='Scheduled Announcements',
                        description='Configure automatic scheduled announcements for your hub',
                        value='hsannounce',
                    )
                )

            embed = discord.Embed(
                description=f"### {self.bot.emotes.gear_icon} Hub Configuration\nCustomize your InterChat hub for your community. Pick what you'd like to configure below.",
                color=self.constants.color(),
            )
            view = ConfigurationView(
                self.bot, ctx.author, db_hub, self.constants, options, user_level
            )
            message = await ctx.send(embed=embed, view=view)
            view.message = message

    @hubo_autocomplete
    @check_user()
    async def delete_hub(self, ctx: commands.Context[commands.Bot], hub: str):
        hub_obj = json.loads(hub)
        async with self.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == hub_obj['id'])
            result = await session.scalar(stmt)

            # confirm here with result found above
            await session.delete(result)
            await session.commit()

        embed = discord.Embed(
            title='Success!',
            description=f'{self.bot.emotes.tick} Your hub has been deleted successfully.',
            color=discord.Color.green(),
        )
        await ctx.send(embed=embed, ephemeral=True)

    @commands.hybrid_command(
        name='connect', description='Join an InterChat hub', extras={'category': 'Hubs'}
    )
    # @commands.has_permissions(manage_channels=True)
    @check_user()
    async def join(
        self,
        ctx: commands.Context[commands.Bot],
        hub: Optional[str] = None,
        invite: Optional[str] = None,
        channel: Optional[discord.TextChannel | discord.Thread] = None,
    ):
        if hub is None and invite is None:
            raise InvalidInput()

        if not ctx.guild:
            raise commands.NoPrivateMessage()

        selected_channel = channel or ctx.channel

        result_invite = None
        result_hub = None

        if invite:
            async with self.bot.db.get_session() as session:
                stmt = select(HubInvite).where(HubInvite.code == invite)
                result_invite = await session.scalar(stmt)

                if not result_invite:
                    raise InvalidInvite()

                if result_invite.expires and result_invite.expires <= datetime.now():
                    await session.delete(result_invite)
                    await session.commit()
                    raise InvalidInvite()

                result_invite.uses += 1

                stmt = select(Hub).where(Hub.id == result_invite.hubId)
                result_hub = await session.scalar(stmt)

                if result_invite.maxUses == result_invite.uses:
                    await session.delete(result_invite)

                await session.commit()

        elif hub:
            async with self.bot.db.get_session() as session:
                stmt = select(Hub).where((Hub.name == hub) & (Hub.private == False))
                result_hub = await session.scalar(stmt)

                if not result_hub:
                    raise InvalidInput()

        if result_hub is None:
            raise InvalidInput()

        webhook = await get_or_create_webhook(self.bot, selected_channel)

        if not webhook:
            raise WebhookError()

        connection = Connection(
            hubId=result_hub.id,
            channelId=str(selected_channel.id),
            webhookURL=webhook.url,
            parentId=(
                str(selected_channel.parent.id)
                if isinstance(selected_channel, discord.Thread)
                else None
            ),
            invite=result_invite.code if result_invite else None,
            serverId=str(ctx.guild.id),
            createdAt=datetime.now(),
            lastActive=datetime.now(),
        )

        try:
            async with self.bot.db.get_session() as session:
                session.add(connection)
                await session.commit()
        except IntegrityError as e:
            if 'duplicate key value violates unique constraint' in str(
                e
            ) and 'Connection_hubId_serverId_key' in str(e):
                embed = discord.Embed(
                    title='Already connected!',
                    description=f'{self.bot.emotes.x_icon} This server is already connected to **{result_hub.name}**!',
                    color=discord.Color.red(),
                )
                return await ctx.send(embed=embed, ephemeral=True)

        embed = discord.Embed(
            title='Connected!',
            description=f'{self.bot.emotes.tick} Connected to **{result_hub.name}** - get chatting!',
            color=self.constants.color(),
        )
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.avatar.url)
        embed.set_footer(text=f'Connected Channel: {selected_channel}')
        await ctx.send(embed=embed)

    @commands.hybrid_command(
        name='disconnect',
        description='Leave an InterChat hub',
        extras={'category': 'Hubs'},
    )
    @commands.has_permissions(manage_channels=True)
    @check_user()
    async def leave(self, ctx: commands.Context[commands.Bot]):
        async with self.bot.db.get_session() as session:
            stmt = select(Connection).where(Connection.channelId == str(ctx.channel.id))
            result = await session.scalar(stmt)

            if not result:
                raise NotConnected()

            await session.delete(result)
            await session.commit()

        await get_and_cleanup_webhooks(self.bot, ctx.channel)

        embed = discord.Embed(
            title='Disconnected!',
            description=f'{self.bot.emotes.tick} Disconnected from hub!',
            color=self.constants.color(),
        )
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.display_avatar.url)
        embed.set_footer(text=f'Disconnected Channel: {ctx.channel}')
        await ctx.send(embed=embed)

    @hub.command(
        name='invites',
        description='View all active invites for a hub',
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    @check_user(True)
    async def invites(self, ctx: commands.Context[commands.Bot], hub: str):
        hub_data = json.loads(hub)
        hub_id = hub_data['id']
        hub_name = hub_data['name']

        async with self.bot.db.get_session() as session:
            stmt = select(HubInvite).where(HubInvite.hubId == hub_id)
            result = await session.execute(stmt)
            all_invites = result.scalars().all()

            expired_invites = []
            active_invites = []

            current_time = datetime.now()

            for invite in all_invites:
                if invite.expires and invite.expires <= current_time:
                    expired_invites.append(invite)
                else:
                    active_invites.append(invite)

            if expired_invites:
                for expired_invite in expired_invites:
                    await session.delete(expired_invite)
                await session.commit()

        embed = discord.Embed(title='Active invites', description=' ', color=self.constants.color())
        embed.set_footer(text=f'Hub: {hub_name}')

        if active_invites:
            for invite in active_invites:
                if invite.maxUses == 0:
                    muses = '∞'
                else:
                    muses = invite.maxUses
                embed.add_field(
                    name=f'{self.bot.emotes.link_icon} ||{invite.code}||',
                    value=f'\n> **Uses:** {invite.uses}/{muses}\n> **Expires:** <t:{int(invite.expires.timestamp())}:R>',
                    inline=True,
                )
        else:
            embed.description = f'{self.bot.emotes.x_icon} None found'

        view = InviteView(self.bot, self.constants, ctx.author, hub_data)
        view.setup_button()
        await ctx.send(embed=embed, view=view, ephemeral=True)


async def setup(bot: commands.Bot):
    await bot.add_cog(Hubs(bot))
