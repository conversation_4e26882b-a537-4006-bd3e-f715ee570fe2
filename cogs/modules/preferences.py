import discord
from discord.ext import commands
from discord.ui import View, Button, button, Select

from sqlalchemy import select, update

from utils.modules.core.checks import interaction_check
from utils.utils import check_user, load_locales
from utils.modules.errors import customDiscord
from utils.constants import InterchatConstants
from utils.modules.core.db.models import User

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot


def _createReturnButton(
    view: View,
    bot: 'Bot',
    user: discord.User | discord.Member,
    constants: 'InterchatConstants',
    row: int = 1,
):
    return_button = But<PERSON>(label='Return', style=discord.ButtonStyle.gray, row=row)

    async def return_callback(interaction: discord.Interaction['Bot']):
        await interaction.response.defer()
        await interaction_check(interaction, user, interaction.user)

        view = PreferencesView(bot, user, constants)
        embed = discord.Embed(
            title='User Preferences',
            description='Edit your personal settings to make InterChat work for you. These are GLOBAL and work across all servers you share with InterChat.',
            color=constants.color(),
        )
        embed.set_author(
            name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
        )

        await interaction.edit_original_response(embed=embed, view=view)

    return_button.callback = return_callback
    view.add_item(return_button)


class LocaleView(View):
    def __init__(self, bot: 'Bot', user: discord.User | discord.Member, constants):
        super().__init__(timeout=120)
        self.bot = bot
        self.user = user
        self.constants = constants

        _createReturnButton(self, bot, user, constants)

    async def load_options(self) -> None:
        options = []
        for locale in await load_locales():
            options.append(
                discord.SelectOption(
                    emoji=locale['flag'], label=locale['long'], value=locale['short']
                )
            )

        for item in self.children:
            if isinstance(item, discord.ui.Select):
                item.options = options
                item.disabled = False
                break

    @discord.ui.select(
        placeholder='Select a language',
        options=[
            discord.SelectOption(label='Loading...', description='Please wait...', value='LOADING')
        ],
        disabled=True,
        max_values=1,
        min_values=1,
    )
    async def on_submit(self, interaction: discord.Interaction['Bot'], discord_select: Select):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        selected_locale = discord_select.values[0]

        async with self.bot.db.get_session() as session:
            stmt = select(User).where(User.id == str(interaction.user.id))
            result = (await session.execute(stmt)).scalar_one()

            result.locale = selected_locale
            await session.commit()

        locale_name = None
        for locale in await load_locales():
            if locale['short'] == selected_locale:
                locale_name = locale['long']
                break

        embed = discord.Embed(
            title='Success!',
            description=f'{self.bot.emotes.tick} Your language has been set to **{locale_name}**',
            color=discord.Color.green(),
        )
        await interaction.edit_original_response(embed=embed, view=None)


class BadgeVisibility(View):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.Member | discord.User,
        constants: 'InterchatConstants',
        current: User,
    ):
        super().__init__(timeout=120)
        self.bot = bot
        self.user = user
        self.constants = constants
        self.current = current
        self.load_button()

    def load_button(self):
        if self.current.showBadges:
            self.callback.label = 'Hide'
            self.callback.style = discord.ButtonStyle.red
        else:
            self.callback.label = 'Show'
            self.callback.style = discord.ButtonStyle.green
        self.callback.disabled = False

    @button(label='Loading...', style=discord.ButtonStyle.grey, disabled=True)
    async def callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        async with self.bot.db.get_session() as session:
            new_value = not self.current.showBadges
            stmt = (
                update(User).where(User.id == str(interaction.user.id)).values(showBadges=new_value)
            )
            await session.execute(stmt)
            await session.commit()

            self.current.showBadges = new_value

            self.load_button()

            embed = discord.Embed(
                title='Preferences: Badges',
                description=f'Your badges are now **{"visible" if new_value else "hidden"}** within your messages. You may toggle this below using the button, or return to the menu with the return button.',
                color=self.constants.color(),
            )
            embed.set_author(
                name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
            )

            await interaction.edit_original_response(embed=embed, view=self)


class MentionReplyView(View):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.Member | discord.User,
        constants: 'InterchatConstants',
        current: User,
    ):
        super().__init__(timeout=120)
        self.bot = bot
        self.user = user
        self.constants = constants
        self.current = current

        _createReturnButton(self, bot, user, constants)
        self.load_button()

    def load_button(self):
        if self.current.mentionOnReply:
            self.callback.label = 'Disable'
            self.callback.style = discord.ButtonStyle.red
        else:
            self.callback.label = 'Enable'
            self.callback.style = discord.ButtonStyle.green
        self.callback.disabled = False

    @button(label='Loading...', style=discord.ButtonStyle.grey, disabled=True)
    async def callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        async with self.bot.db.get_session() as session:
            new_value = not self.current.mentionOnReply
            stmt = (
                update(User)
                .where(User.id == str(interaction.user.id))
                .values(mentionOnReply=new_value)
            )
            await session.execute(stmt)
            await session.commit()

            self.current.mentionOnReply = new_value
            self.load_button()

            embed = discord.Embed(
                title='Preferences: Reply Mentions',
                description=f'You will now be **{"mentioned" if new_value else "not mentioned"}** when someone replies to your messages. You may toggle this below using the button, or return to the menu with the return button.',
                color=self.constants.color(),
            )
            embed.set_author(
                name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
            )

            await interaction.edit_original_response(embed=embed, view=self)


class GeneralView(View):
    def __init__(self, bot: 'Bot', user: discord.Member | discord.User, constants):
        super().__init__(timeout=120)
        self.bot = bot
        self.user = user
        self.constants = constants
        self.setup_options()

        _createReturnButton(self, bot, user, constants)

    def setup_options(self):
        options = [
            discord.SelectOption(
                emoji=self.bot.emotes.lightbulb_icon,
                label='Badge Visibility',
                description='Badge visibility on Hub messages',
                value='bvis',
            ),
            discord.SelectOption(
                emoji=self.bot.emotes.mention_icon,
                label='Mention On Reply',
                description='Should you be mentioned on a message reply?',
                value='mreply',
            ),
        ]

        for item in self.children:
            if isinstance(item, discord.ui.Select):
                item.options = options
                item.disabled = False
                item.placeholder = 'Select an option'
                break

    @discord.ui.select(
        placeholder='Loading...',
        options=[discord.SelectOption(label='Loading...', value='NONE')],
        disabled=True,
        max_values=1,
        min_values=1,
    )
    async def on_submit(self, interaction: discord.Interaction['Bot'], discord_select: Select):
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        async with self.bot.db.get_session() as session:
            stmt = select(User).where(User.id == str(interaction.user.id))
            result = (await session.execute(stmt)).scalar()

        if not result:
            embed = discord.Embed(
                title='Error!',
                description=f'{self.bot.emotes.x_icon} You do not have preferences set up yet.',
                color=self.constants.color(),
            )
            return await interaction.edit_original_response(embed=embed, view=None)

        match discord_select.values[0]:
            case 'bvis':
                embed = discord.Embed(
                    title='Preferences: Badges',
                    description=f'Currently your badges are **{"visible" if result.showBadges else "hidden"}** within your messages. You may toggle this below using the button, or return to the menu with the return button.',
                    color=self.constants.color(),
                )
                embed.set_author(
                    name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
                )
                view = BadgeVisibility(self.bot, self.user, self.constants, result)
                await interaction.edit_original_response(embed=embed, view=view)

            case 'mreply':
                embed = discord.Embed(
                    title='Preferences: Reply Mentions',
                    description=f'Currently you are **{"mentioned" if result.mentionOnReply else "not mentioned"}** when replied to within your messages. You may toggle this below using the button, or return to the menu with the return button.',
                    color=self.constants.color(),
                )
                embed.set_author(
                    name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
                )
                view = MentionReplyView(self.bot, self.user, self.constants, result)
                await interaction.edit_original_response(embed=embed, view=view)

            case _:
                raise customDiscord.InvalidInput()


class PreferencesView(View):
    def __init__(self, bot: 'Bot', user: discord.User | discord.Member, constants):
        super().__init__(timeout=120)
        self.bot = bot
        self.user = user
        self.constants = constants
        self.load_options()

    def load_options(self):
        options = [
            discord.SelectOption(
                emoji=self.bot.emotes.globe_icon,
                label='Language',
                description='Change the language the bot responds to you in.',
                value='locale',
            ),
            discord.SelectOption(
                emoji=self.bot.emotes.gear_icon,
                label='General',
                description='Edit badge visibility, reply preferences and more!',
                value='general',
            ),
        ]
        for item in self.children:
            if isinstance(item, discord.ui.Select):
                item.options = options
                item.disabled = False
                item.placeholder = 'Select a category'
                break

    @discord.ui.select(
        placeholder='Loading...',
        options=[
            discord.SelectOption(label='Loading...', description='Please wait...', value='LOADING')
        ],
        disabled=True,
        max_values=1,
        min_values=1,
    )
    async def on_submit(
        self, interaction: discord.Interaction['Bot'], select: discord.ui.Select[View]
    ) -> None:
        await interaction.response.defer()
        await interaction_check(interaction, self.user, interaction.user)

        match select.values[0]:
            case 'locale':
                view = LocaleView(self.bot, self.user, self.constants)
                await view.load_options()
                embed = discord.Embed(
                    title='Language Select',
                    description='Select a language from the select below. This will globally change the language you see across the bot. Only available languages will show.',
                    color=self.constants.color(),
                )
                embed.set_author(
                    name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
                )
                await interaction.edit_original_response(embed=embed, view=view)

            case 'general':
                embed = discord.Embed(
                    title='General Preferences',
                    description='Configure general preferences within InterChat. This ensures your experience is the best we are able to make it.',
                    color=self.constants.color(),
                )
                embed.set_author(
                    name=f'@{interaction.user.name}', icon_url=interaction.user.display_avatar.url
                )
                view = GeneralView(self.bot, self.user, self.constants)
                await interaction.edit_original_response(embed=embed, view=view)

            case _:
                raise customDiscord.InvalidInput()


class Preferences(commands.Cog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = InterchatConstants()

    @commands.hybrid_group()
    async def configure(self, ctx: commands.Context['Bot']): ...

    @configure.command()
    @check_user()
    async def preferences(self, ctx: commands.Context['Bot']):
        await ctx.defer(ephemeral=True)
        embed = discord.Embed(
            title='User Preferences',
            description='Edit your personal settings to make InterChat work for you. These are GLOBAL and work across all servers you share with InterChat.',
            color=self.constants.color(),
        )
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.display_avatar.url)
        view = PreferencesView(self.bot, ctx.author, self.constants)
        await ctx.send(embed=embed, view=view, ephemeral=True)


async def setup(bot: 'Bot') -> None:
    await bot.add_cog(Preferences(bot))
