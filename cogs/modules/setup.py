from datetime import datetime
import discord
from discord.ext import commands
from discord.ui import View, select, Select, button, Button, TextInput

from utils.modules.core.db.models import Hub
from utils.modules.core.checks import interaction_check
from utils.modules.ui.CreateEmbed import create_embed, get_text
from utils.modules.ui.CustomModal import CustomModal
from utils.utils import load_user_locale, check_user
from utils.constants import InterchatConstants

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot


class CreateView(View):
    def __init__(self, bot, locale, user, constants: InterchatConstants):
        super().__init__(timeout=300)
        self.bot: Bot = bot
        self.locale = locale
        self.user = user
        self.constants = constants
        self.hub_data: dict[str, str] = {}

    async def update_button(self, interaction: discord.Interaction['Bot']):
        # Update button states based on hub_data
        self.create_callback.disabled = not self.hub_data
        self.create_callback.style = (
            discord.ButtonStyle.secondary if not self.hub_data else discord.ButtonStyle.green
        )

        if self.hub_data:
            self.hub_info_callback.label = 'Hub Information Complete'
            self.hub_info_callback.emoji = self.bot.emotes.tick_icon
            self.hub_info_callback.style = discord.ButtonStyle.secondary

            self.create_callback.label = 'Create Hub'
            self.create_callback.emoji = self.bot.emotes.wand_icon
        else:
            self.hub_info_callback.label = 'Enter Hub Information'
            self.hub_info_callback.emoji = self.bot.emotes.edit_icon
            self.hub_info_callback.style = discord.ButtonStyle.primary
            self.create_callback.label = 'Complete Hub Info First'
            self.create_callback.emoji = self.bot.emotes.x_icon

        if interaction.response.is_done():
            await interaction.edit_original_response(view=self)
        else:
            await interaction.response.edit_message(view=self)

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True

    @button(label='Back', style=discord.ButtonStyle.secondary, emoji='⬅️', row=1)
    async def back_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        if not await interaction_check(interaction, self.user, interaction.user):
            await interaction.response.defer()
            return

        embed = create_embed(
            self.locale,
            'commands.setup.welcome.title',
            'commands.setup.welcome.description',
        )

        options = [
            discord.SelectOption(
                emoji=str(self.bot.emotes.plus_icon),
                label='Create a Hub',
                description='Start your own InterChat community',
                value='create',
            ),
            discord.SelectOption(
                emoji=str(self.bot.emotes.chat_icon),
                label='Join a Hub',
                description='Connect to an existing community',
                value='join',
            ),
        ]

        view = SetupView(self.bot, self.locale, self.user, self.constants, options)
        view.set_options()
        await interaction.response.edit_message(embed=embed, view=view)

    @button(label='📝 Enter Hub Information', style=discord.ButtonStyle.primary, row=0)
    async def hub_info_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        if not await interaction_check(interaction, self.user, interaction.user):
            await interaction.response.defer()
            return

        modal = CustomModal(
            get_text(self.locale, 'commands.setup.hubCreation.modal.title'),
            [
                (
                    'name',
                    TextInput(
                        label=get_text(self.locale, 'commands.setup.hubCreation.modal.name.label'),
                        placeholder=get_text(
                            self.locale,
                            'commands.setup.hubCreation.modal.name.placeholder',
                        ),
                        max_length=50,
                        required=True,
                    ),
                ),
                (
                    'short',
                    TextInput(
                        label='Brief Description',
                        placeholder="A quick summary of your hub's purpose",
                        max_length=140,
                        required=True,
                    ),
                ),
                (
                    'description',
                    TextInput(
                        label=get_text(
                            self.locale,
                            'commands.setup.hubCreation.modal.description.label',
                        ),
                        placeholder=get_text(
                            self.locale,
                            'commands.setup.hubCreation.modal.description.placeholder',
                        ),
                        style=discord.TextStyle.paragraph,
                        max_length=1000,
                        required=True,
                    ),
                ),
                (
                    'url',
                    TextInput(
                        label='Hub Logo URL',
                        placeholder='https://example.com/your-logo.png',
                        required=True,
                    ),
                ),
            ],
        )

        await interaction.response.send_modal(modal)
        await modal.wait()

        self.hub_data = {
            'name': modal.name.value,
            'short': modal.short.value,
            'description': modal.description.value,
            'url': modal.url.value,
        }

        preview_embed = discord.Embed(
            title=f'{self.bot.emotes.tick_icon} Hub Information Saved!',
            description="Here's a preview of your hub:",
            color=self.constants.color(),
        )
        preview_embed.add_field(
            name=f'{self.bot.emotes.hash_icon} Name',
            value=self.hub_data['name'],
            inline=True,
        )
        preview_embed.add_field(
            name=f'{self.bot.emotes.chat_icon} Brief',
            value=self.hub_data['short'],
            inline=True,
        )
        preview_embed.add_field(
            name=f'{self.bot.emotes.description} Description',
            value=(
                self.hub_data['description'][:150] + '...'
                if len(self.hub_data['description']) > 150
                else self.hub_data['description']
            ),
            inline=False,
        )

        preview_embed.set_thumbnail(url=self.hub_data['url'])
        preview_embed.set_footer(text='Ready to create? Click the Create Hub button!')

        await interaction.followup.send(embed=preview_embed, ephemeral=True)
        await self.update_button(interaction)

    @button(
        label='Complete Hub Info First',
        style=discord.ButtonStyle.secondary,
        row=0,
        disabled=True,
    )
    async def create_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()
        if not await interaction_check(interaction, self.user, interaction.user):
            return

        loading_embed = discord.Embed(
            title=f'{self.bot.emotes.loading} Creating Your Hub...',
            description='Please wait while we set up your community space.',
            color=discord.Color.orange(),
        )
        await interaction.edit_original_response(embed=loading_embed, view=None)

        try:
            async with self.bot.db.get_session() as session:
                hub = Hub(
                    name=self.hub_data['name'],
                    shortDescription=self.hub_data['short'],
                    description=self.hub_data['description'],
                    ownerId=str(interaction.user.id),
                    iconUrl=self.hub_data['url'],
                    rules=[],
                    createdAt=datetime.now(),
                    updatedAt=datetime.now(),
                    lastActive=datetime.now(),
                )
                session.add(hub)
                await session.commit()

            success_embed = discord.Embed(
                title=get_text(self.locale, 'commands.setup.nextSteps.created.title'),
                description=get_text(
                    self.locale,
                    'commands.setup.nextSteps.created.description',
                    hubName=self.hub_data['name'],
                ),
                color=self.constants.color(),
            )

            success_embed.add_field(
                name=get_text(self.locale, 'commands.setup.nextSteps.created.inviteLink.title'),
                value=get_text(
                    self.locale,
                    'commands.setup.nextSteps.created.inviteLink.description',
                    hubInviteCommand='/hub invite create',
                    hubName=self.hub_data['name'],
                ),
                inline=False,
            )

            success_embed.add_field(
                name=get_text(self.locale, 'commands.setup.nextSteps.created.shareHub.title'),
                value=get_text(
                    self.locale,
                    'commands.setup.nextSteps.created.shareHub.description',
                    dot=str(self.bot.emotes.dot),
                    supportInvite=self.constants.support_invite(),
                ),
                inline=False,
            )

            success_embed.add_field(
                name=get_text(
                    self.locale,
                    'commands.setup.nextSteps.created.proTips.title',
                    dot=str(self.bot.emotes.dot),
                ),
                value=get_text(
                    self.locale,
                    'commands.setup.nextSteps.created.proTips.description',
                    dot=str(self.bot.emotes.dot),
                    website='https://interchat.app',
                    hubVisibilityCommand='/hub visibility',
                    supportInvite=self.constants.support_invite(),
                ),
                inline=False,
            )

            try:
                success_embed.set_thumbnail(url=self.hub_data['url'])
            except:
                pass

            success_embed.set_footer(
                text=f'Hub created by {interaction.user.display_name}',
                icon_url=interaction.user.display_avatar.url,
            )

            await interaction.edit_original_response(embed=success_embed, view=None)

        except Exception:
            error_embed = discord.Embed(
                title=f'{self.bot.emotes.no} Hub Creation Failed',
                description=get_text(self.locale, 'commands.setup.errors.hubCreationFailed'),
                color=discord.Color.red(),
            )
            error_embed.add_field(
                name=f'{self.bot.emotes.gear_icon} What you can do:',
                value='• Check that your logo URL is valid\n• Try again in a few moments\n• Contact support if this persists',
                inline=False,
            )
            await interaction.edit_original_response(embed=error_embed, view=None)


class SetupView(View):
    def __init__(self, bot, locale, user, constants, options):
        super().__init__(timeout=300)
        self.bot: Bot = bot
        self.user = user
        self.constants = constants
        self.locale = locale
        self.options = options

    def set_options(self):
        self.on_submit.options = self.options

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True

    @select(
        placeholder='Choose an option to get started...',
        options=[
            discord.SelectOption(label='Loading...', description='Please wait', value='loading')
        ],
        max_values=1,
        min_values=1,
    )
    async def on_submit(self, interaction: discord.Interaction['Bot'], select: Select):
        if not await interaction_check(interaction, self.user, interaction.user):
            await interaction.response.defer()
            return

        if select.values[0] == 'create':
            embed = discord.Embed(
                title=f"{self.bot.emotes.lightbulb_icon} What You'll Create:",
                description=f'{self.bot.emotes.dot} A unique community space for your servers\n{self.bot.emotes.dot} Complete control over rules and moderation\n{self.bot.emotes.dot} Custom settings and features\n{self.bot.emotes.dot} Private by default - invite only',
                color=self.constants.color(),
            )
            embed.add_field(
                name=f"{self.bot.emotes.info_icon} You'll Need:",
                value=f'{self.bot.emotes.arrow_right} Creative hub name\n{self.bot.emotes.arrow_right} Brief description\n{self.bot.emotes.arrow_right} Detailed description\n{self.bot.emotes.arrow_right} Logo image URL',
                inline=False,
            )

            embed.set_footer(text="Click 'Enter Hub Information' below to get started!")

            view = CreateView(self.bot, self.locale, self.user, self.constants)
            await view.update_button(interaction)
            await interaction.response.edit_message(embed=embed, view=view)

        elif select.values[0] == 'join':
            embed = create_embed(
                self.locale,
                'commands.setup.hubChoice.popularHubs.title',
                'commands.setup.hubChoice.popularHubs.description',
            )

            embed.add_field(
                name=f'{self.bot.emotes.star} Why Join Popular Hubs?',
                value=f'{self.bot.emotes.dot} Connect with thousands of active users\n{self.bot.emotes.dot} Start chatting immediately\n{self.bot.emotes.dot} No setup required - just connect and go!\n{self.bot.emotes.dot} Perfect introduction to InterChat',
                inline=False,
            )

            embed.add_field(
                name=f'{self.bot.emotes.search_icon} How to Join:',
                value=f'{self.bot.emotes.arrow_right} Browse communities on [interchat.app/hubs](https://interchat.app/hubs)\n{self.bot.emotes.arrow_right} Ask hub owners for invite links\n{self.bot.emotes.arrow_right} Use `/hub search` to find specific topics',
                inline=False,
            )

            embed.add_field(
                name=get_text(self.locale, 'commands.setup.hubChoice.note'),
                value=f'{self.bot.emotes.plus_icon} You can connect to multiple hubs at once!',
                inline=False,
            )

            back_view = View(timeout=300)

            @button(
                label='Back',
                style=discord.ButtonStyle.secondary,
                emoji=self.bot.emotes.previous,
                row=1,
            )
            async def back_to_menu(self, interaction: discord.Interaction['Bot'], button: Button):
                if not await interaction_check(interaction, self.user, interaction.user):
                    await interaction.response.defer()
                    return

                main_embed = create_embed(
                    self.locale,
                    'commands.setup.welcome.title',
                    'commands.setup.welcome.description',
                )
                main_view = SetupView(
                    self.bot, self.locale, self.user, self.constants, self.options
                )
                main_view.set_options()
                await interaction.response.edit_message(embed=main_embed, view=main_view)

            back_view.add_item(back_to_menu)
            await interaction.response.edit_message(embed=embed, view=back_view)


class Setup(commands.Cog):
    def __init__(self, bot):
        self.bot: Bot = bot
        self.constants = InterchatConstants()

    @commands.hybrid_command(
        name='setup',
        description=(
            get_text('en', 'commands.setup.description')
            if hasattr(__builtins__, 'get_text')
            else 'Setup InterChat for your server'
        ),
        extras={'category': 'Server'},
    )
    @check_user()
    async def setup(self, ctx: commands.Context):
        locale = await load_user_locale(self.bot, ctx)

        embed = create_embed(
            locale, 'commands.setup.welcome.title', 'commands.setup.welcome.description'
        )

        embed.set_footer(
            text=f'InterChat Setup • {ctx.guild.name}',
            icon_url=ctx.guild.icon.url if ctx.guild.icon else None,
        )

        options = [
            discord.SelectOption(
                emoji=str(self.bot.emotes.plus_icon),
                label='Create a Hub',
                description='Start your own InterChat community',
                value='create',
            ),
            discord.SelectOption(
                emoji=str(self.bot.emotes.chat_icon),
                label='Join a Hub',
                description='Connect to an existing community',
                value='join',
            ),
        ]

        view = SetupView(self.bot, locale, ctx.author, self.constants, options)
        view.set_options()
        await ctx.send(embed=embed, view=view, ephemeral=True)


async def setup(bot):
    await bot.add_cog(Setup(bot))
