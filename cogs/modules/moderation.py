import json
import discord
from discord.ext import commands
from discord.ui import TextInput
from sqlalchemy import select
from datetime import datetime
from typing import TYPE_CHECKING, Literal, Optional, Union

from utils.modules.core.db.models import (
    Infraction,
    InfractionType,
    InfractionStatus,
    Hub,
)
from utils.modules.errors.customDiscord import NoInteraction, InvalidInput
from utils.modules.events.eventDispatcher import HubEventType
from utils.modules.events.hubLoggingHelpers import HubLogger
from utils.modules.ui.AutoComplete import hubm_autocomplete
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.ui.ModerationViews import HubSelectionView, ModPanelView
from utils.modules.core.moderation import (
    ban_server,
    ban_user,
    blacklist_server,
    blacklist_user,
    get_user_moderated_hubs,
    fetch_original_message,
    mod_panel_embed,
    warn_user,
)
from utils.modules.core.appealHandler import AppealView
from utils.constants import InterchatConstants
from utils.utils import duration_to_datetime, parse_duration

if TYPE_CHECKING:
    from main import Bot

    SupportedModActions = Literal['warn', 'mute', 'ban', 'blacklist']


class Moderation(commands.Cog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = InterchatConstants()
        self.locale = 'en'

    @commands.hybrid_group()
    async def mod(self, _ctx: commands.Context[commands.Bot]):
        """Moderation commands for hub management."""
        pass

    async def _fetch_hub(self, hub_id: str) -> Optional[Hub]:
        async with self.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == hub_id)
            return (await session.execute(stmt)).scalar_one_or_none()

    @mod.command(
        name='panel',
        description='Open the moderation panel for users, messages, or servers',
    )
    async def panel(
        self,
        ctx: commands.Context[commands.Bot],
        user: Optional[discord.User] = None,
        message: discord.Message = None,
        server: discord.Guild = None,
    ):
        """
        Args:
            user: User to moderate
            message: Message to moderate
            server: Server to moderate
        """
        await ctx.defer()

        if user == ctx.author:
            raise InvalidInput()

        user_hubs = await get_user_moderated_hubs(self.bot, str(ctx.author.id))
        if not user_hubs:
            return await ctx.send(
                f"{self.bot.emotes.x_icon} You don't have moderation permissions in any hubs.",
                ephemeral=True,
            )

        # Case 4: Message ID provided - fetch message details
        if message is not None:
            original_message = await fetch_original_message(self.bot, str(message.id))
            if not original_message:
                return await ctx.send(
                    f'{self.bot.emotes.x_icon} Original message not found in database.',
                    ephemeral=True,
                )

            # Extract message details
            try:
                target_user = await self.bot.fetch_user(int(original_message.authorId))
                target_server = await self.bot.fetch_guild(int(original_message.guildId))
            except (discord.NotFound, ValueError):
                return await ctx.send(
                    f'{self.bot.emotes.x_icon} Could not fetch message author or server.',
                    ephemeral=True,
                )

            # Get the hub from the message
            async with self.bot.db.get_session() as session:
                stmt = select(Hub).where(Hub.id == original_message.hub_id)
                selected_hub = (await session.execute(stmt)).scalar_one_or_none()

            if not selected_hub:
                return await ctx.send(
                    f'{self.bot.emotes.x_icon} Hub not found for this message.',
                    ephemeral=True,
                )

            # Show the mod panel directly
            view = ModPanelView(
                self.bot,
                ctx.author,
                target_user,
                target_server,
                message,
                selected_hub,
                self.constants,
                self.locale,
            )

            embed = mod_panel_embed(
                self.bot,
                selected_hub,
                target_user,
                target_server,
                message,
                user_reputation=69,
                user_infractions=69,
                server_infractions=69,
                _locale=self.locale,
            )
            await ctx.send(embed=embed, view=view)

        else:
            # Cases 1-3: No message provided, show hub selection first
            if not user and not server:
                return await ctx.send(
                    f'{self.bot.emotes.x_icon} You must provide either a user, server, or message parameter.',
                    ephemeral=True,
                )

            # Show hub selection view
            view = HubSelectionView(
                self.bot,
                ctx.author,
                user,
                server,
                None,  # No message
                user_hubs,
                self.constants,
                self.locale,
            )

            embed = discord.Embed(
                title='Hub Selection',
                description='Select a hub to perform moderation actions:',
                color=self.constants.color(),
            )

            if user:
                embed.add_field(
                    name='Target User',
                    value=f'{user.mention} (`{user.id}`)',
                    inline=True,
                )
            if server:
                embed.add_field(
                    name='Target Server',
                    value=f'**{server.name}** (`{server.id}`)',
                    inline=True,
                )

            embed.add_field(
                name='Hub',
                value='Select a hub from the dropdown below.',
                inline=False,
            )

            await ctx.send(embed=embed, view=view)

    async def _base_mod_action(
        self,
        ctx: commands.Context['Bot'],
        action: Union[str, 'SupportedModActions'],
        hub_id: str,
        user: Optional[discord.User] = None,
        server: Optional[discord.Guild] = None,
    ):
        if user and server:
            await ctx.send(
                f'{self.bot.emotes.x_icon} Please specify either a user or a server, not both.',
                ephemeral=True,
            )
            return
        if not user and not server:
            await ctx.send(
                f'{self.bot.emotes.x_icon} Please specify either a user or a server.',
                ephemeral=True,
            )

        selected_hub = await self._fetch_hub(hub_id)
        if not selected_hub:
            await ctx.send(
                f'{self.bot.emotes.x_icon} Hub not found.',
                ephemeral=True,
            )
            return

        handler = ModerationActionHandler(
            self.bot,
            ctx.author,
            selected_hub,
            self.constants,
            self.locale,
        )

        if not ctx.interaction:
            raise NoInteraction()

        if user:
            await handler.handle_user_action(ctx.interaction, action, user)
        elif server:
            await handler.handle_server_action(ctx.interaction, action, server)

    @mod.command(
        name='ban',
        description='Ban a user or server from a hub.',
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def ban(
        self,
        ctx: commands.Context['Bot'],
        hub: str,
        user: Optional[discord.User] = None,
        server: discord.Guild = None,
    ):
        """
        Args:
            hub: Hub to ban from (Hub ID also accepted)
            user: User (or their ID)to ban
            server: ID of the server to ban
        """
        hub_obj = json.loads(hub)
        await self._base_mod_action(ctx, 'ban', hub_obj['id'], user, server)

    @mod.command(
        name='mute',
        description='Mute a user or server from a hub.',
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def mute(
        self,
        ctx: commands.Context['Bot'],
        hub: str,
        user: Optional[discord.User] = None,
        server: discord.Guild = None,
    ):
        """
        Args:
            hub: Hub to mute from (Hub ID also accepted)
            user: User to mute
            server: Server to mute
        """
        hub_obj = json.loads(hub)
        await self._base_mod_action(ctx, 'mute', hub_obj['id'], user, server)

    @mod.command(
        name='warn',
        description='Warn a user or server from a hub.',
        extras={'category': 'Moderation'},
    )
    @hubm_autocomplete
    async def warn(
        self,
        ctx: commands.Context['Bot'],
        hub: str,
        user: Optional[discord.User] = None,
        server: discord.Guild = None,
    ):
        """
        Args:
            hub: Hub to warn from (Hub ID also accepted)
            user: User to warn
            server: Server to warn
        """
        hub_obj = json.loads(hub)
        await self._base_mod_action(ctx, 'warn', hub_obj['id'], user, server)


async def setup(bot):
    await bot.add_cog(Moderation(bot))


class ModerationActionHandler:
    """Handler for executing moderation actions."""

    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        selected_hub: Hub,
        constants,
        locale: str,
    ):
        self.bot = bot
        self.moderator = moderator
        self.selected_hub = selected_hub
        self.constants = constants
        self.locale = locale

    async def handle_user_action(
        self,
        ctx: discord.Interaction['Bot'],
        action: Union[str, 'SupportedModActions'],
        target_user: discord.User | discord.Member,
    ):
        """Handle moderation actions targeting a user."""
        if action == 'warn':
            await self._create_warn_modal(ctx, target_user)
        elif action == 'mute':
            await self._create_mute_modal(ctx, target_user)
        elif action == 'ban':
            await self._create_ban_modal(ctx, target_user, None)
        elif action == 'blacklist':
            await self._create_blacklist_modal(ctx, target_user, None)
        else:
            await ctx.response.send_message(
                f'{self.bot.emotes.x_icon} Unknown action: {action}',
                ephemeral=True,
            )

    async def handle_server_action(
        self,
        ctx: discord.Interaction['Bot'],
        action: Union[str, 'SupportedModActions'],
        target_server: discord.Guild,
    ):
        """Handle moderation actions targeting a server."""
        if action in ['warn', 'mute']:
            await ctx.response.send_message(
                f'{self.bot.emotes.x_icon} Cannot {action} a server. Use ban or blacklist instead.',
                ephemeral=True,
            )
        elif action == 'ban':
            await self._create_ban_modal(ctx, None, target_server)
        elif action == 'blacklist':
            await self._create_blacklist_modal(ctx, None, target_server)
        else:
            await ctx.response.send_message(
                f'{self.bot.emotes.x_icon} Unknown action: {action}',
                ephemeral=True,
            )

    async def handle_delete_message(self, ctx: discord.Interaction['Bot'], _: discord.Message):
        """Handle message deletion across hubs."""
        # TODO: Implement message deletion across all connected hubs
        await ctx.response.send_message(
            f'{self.bot.emotes.x_icon} Message deletion across hubs is not yet implemented.',
            ephemeral=True,
        )

    async def _create_warn_modal(
        self,
        ctx: discord.Interaction['Bot'],
        target_user: discord.User | discord.Member,
    ):
        """Create and show a modal for collecting warning reason."""
        modal = CustomModal(
            title='Warn User',
            options=[
                (
                    'reason',
                    TextInput(
                        label='Reason',
                        placeholder='Enter the reason for warning this user...',
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
            ],
            callback=lambda modal, inter: self._handle_warn_submission(modal, inter, target_user),
        )

        await ctx.response.send_modal(modal)

    async def _create_mute_modal(
        self,
        ctx: discord.Interaction['Bot'],
        target_user: discord.User | discord.Member,
    ):
        """Create and show a modal for collecting mute reason and duration."""
        modal = CustomModal(
            title='Mute User',
            options=[
                (
                    'reason',
                    TextInput(
                        label='Reason',
                        placeholder='Enter the reason for muting this user...',
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
                (
                    'duration',
                    TextInput(
                        label='Duration',
                        placeholder='Duration (e.g., 1d, 1w, 1m, 1y). Leave blank for permanent.',
                        max_length=10,
                        required=False,
                    ),
                ),
            ],
            callback=lambda modal, inter: self._handle_mute_submission(modal, inter, target_user),
        )
        await ctx.response.send_modal(modal)

    async def _create_ban_modal(
        self,
        ctx: discord.Interaction['Bot'],
        target_user: Optional[discord.User | discord.Member],
        target_server: discord.Guild,
    ):
        """Create and show a modal for collecting ban reason."""
        target_type = 'User' if target_user else 'Server'

        modal = CustomModal(
            title=f'Ban {target_type}',
            options=[
                (
                    'reason',
                    TextInput(
                        label='Reason',
                        placeholder=f'Enter the reason for banning this {target_type.lower()}...',
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
            ],
            callback=lambda modal, inter: self._handle_ban_submission(
                modal, inter, target_user, target_server
            ),
        )
        await ctx.response.send_modal(modal)

    async def _create_blacklist_modal(
        self,
        interaction: discord.Interaction['Bot'],
        target_user: Optional[discord.User | discord.Member],
        target_server: discord.Guild,
    ):
        """Create and show a modal for collecting blacklist reason and duration."""
        target_type = 'User' if target_user else 'Server'

        modal = CustomModal(
            title=f'Blacklist {target_type}',
            options=[
                (
                    'reason',
                    TextInput(
                        label='Reason',
                        placeholder=f'Enter the reason for blacklisting this {target_type.lower()}...',
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
                (
                    'duration',
                    TextInput(
                        label='Duration',
                        placeholder='Duration (e.g., 1d, 1w, 1m, 1y). Leave blank for permanent.',
                        max_length=10,
                        required=False,
                    ),
                ),
            ],
            callback=lambda modal, inter: self._handle_blacklist_submission(
                modal, inter, target_user, target_server
            ),
        )
        await interaction.response.send_modal(modal)

    async def _handle_warn_submission(
        self,
        modal: CustomModal,
        ctx: discord.Interaction['Bot'],
        target_user: discord.User | discord.Member,
    ):
        """Handle the submission of the warn modal."""
        reason = modal.saved_items['reason'].value

        warn = await warn_user(
            db=self.bot.db,
            user_id=str(target_user.id),
            hub_id=self.selected_hub.id,
            mod_id=str(self.moderator.id),
            reason=reason,
        )

        embed = discord.Embed(
            title=f'{self.bot.emotes.edit_icon} Warning Issued',
            description=f'Successfully warned {target_user.mention}',
            color=discord.Color.orange(),
        )
        embed.add_field(name='Reason', value=reason, inline=False)
        embed.add_field(name='Moderator', value=self.moderator.mention, inline=True)
        embed.add_field(name='Hub', value=self.selected_hub.name, inline=True)
        embed.add_field(
            name='Expires',
            value=(f'<t:{int(warn.expiresAt.timestamp())}:R>' if warn.expiresAt else 'Never'),
            inline=True,
        )

        try:
            await ctx.followup.send(embed=embed, ephemeral=True)
        finally:
            await HubLogger.log_user_warn(
                hub_id=self.selected_hub.id,
                hub_name=self.selected_hub.name,
                moderator_id=str(self.moderator.id),
                moderator_name=str(self.moderator),
                user_id=str(target_user.id),
                user_name=str(target_user),
                reason=reason,
            )

    async def _handle_mute_submission(
        self,
        modal: CustomModal,
        ctx: discord.Interaction['Bot'],
        target: discord.User | discord.Member | discord.Guild,
    ):
        """Handle the submission of the mute modal."""
        reason = modal.saved_items['reason'].value
        duration_str = (
            modal.saved_items['duration'].value.strip()
            if modal.saved_items['duration'].value
            else ''
        )

        duration_ms = parse_duration(duration_str) if duration_str else None
        expires_at = duration_to_datetime(duration_ms)

        embed = discord.Embed(
            title=f'{self.bot.emotes.clock_icon} Muted',
            color=discord.Color.orange(),
        )

        if isinstance(target, discord.Guild):
            await ban_server(
                bot=self.bot,
                server_id=str(target.id),
                hub_id=self.selected_hub.id,
                mod_id=str(self.moderator.id),
                reason=reason,
            )
            embed.description = f'Successfully muted server **{target.name}**'
        else:
            await ban_user(
                bot=self.bot,
                user_id=str(target.id),
                hub_id=self.selected_hub.id,
                mod_id=str(self.moderator.id),
                reason=reason,
                duration=duration_ms,
            )
            embed.description = f'Successfully muted user **{target.name}**'

        embed.add_field(name='Reason', value=reason, inline=False)
        embed.add_field(name='Moderator', value=self.moderator.mention, inline=True)
        embed.add_field(name='Hub', value=self.selected_hub.name, inline=True)

        if expires_at:
            embed.add_field(name='Duration', value=duration_str, inline=True)
            embed.set_footer(text=f'Expires: {expires_at.strftime("%Y-%m-%d %H:%M:%S")}')
        else:
            embed.add_field(name='Duration', value='Permanent', inline=True)

        try:
            await ctx.followup.send(embed=embed, ephemeral=True)
        finally:
            await HubLogger.log_mute(
                action_type=(
                    HubEventType.USER_MUTE
                    if isinstance(target, discord.User)
                    else HubEventType.SERVER_MUTE
                ),
                hub_id=self.selected_hub.id,
                hub_name=self.selected_hub.name,
                moderator_id=str(self.moderator.id),
                moderator_name=str(self.moderator),
                target_id=str(target.id),
                target_name=str(target),
                reason=reason,
                duration=duration_str,
                expires_at=expires_at,
            )

    async def _handle_ban_submission(
        self,
        modal: CustomModal,
        ctx: discord.Interaction['Bot'],
        target_user: Optional[discord.User | discord.Member],
        target_server: discord.Guild,
    ):
        """Handle the submission of the ban modal."""
        reason = modal.saved_items['reason'].value

        async with self.bot.db.get_session() as session:
            ban_infraction = Infraction(
                hubId=self.selected_hub.id,
                moderatorId=str(self.moderator.id),
                userId=str(target_user.id) if target_user else None,
                serverId=str(target_server.id) if target_server else None,
                serverName=target_server.name if target_server else None,
                reason=reason,
                expiresAt=None,  # Bans are permanent, they are?
                type=InfractionType.BAN,
                status=InfractionStatus.ACTIVE,
                notified=False,
                createdAt=datetime.now(),
                updatedAt=datetime.now(),
            )
            session.add(ban_infraction)
            await session.commit()

            target_type = 'user' if target_user else 'server'
            if target_user:
                target_name = target_user.display_name
            elif target_server:
                target_name = target_server.name
            else:
                target_name = 'Unknown'

            embed = discord.Embed(
                title=f'{self.bot.emotes.hammer_icon} {target_type.capitalize()} Banned',
                description=f'Successfully banned {target_type} **{target_name}**',
                color=discord.Color.red(),
            )
            embed.add_field(name='Reason', value=reason, inline=False)
            embed.add_field(name='Moderator', value=self.moderator.mention, inline=True)
            embed.add_field(name='Hub', value=self.selected_hub.name, inline=True)

            try:
                await ctx.followup.send(embed=embed, ephemeral=True)
            finally:
                if target_user:
                    await HubLogger.log_ban(
                        action_type=(
                            HubEventType.USER_BAN if target_user else HubEventType.SERVER_BAN
                        ),
                        hub_id=self.selected_hub.id,
                        hub_name=self.selected_hub.name,
                        moderator_id=str(self.moderator.id),
                        moderator_name=str(self.moderator),
                        target_id=str(target_user.id),
                        target_name=str(target_user),
                        reason=reason,
                    )

                if target_user:
                    embed = discord.Embed(
                        title='Hub Ban',
                        description=f'You have been banned in **{self.selected_hub.name}**. Your interaction within this hub is now restricted. You may appeal using the below button.',
                        color=self.constants.color(),
                    )
                    embed.add_field(name='Expiry', value='Never', inline=False)
                    embed.add_field(name='Reason', value=reason, inline=False)
                    view = AppealView(self.bot, target_user, self.constants)
                    await target_user.send(embed=embed, view=view)

    async def _handle_blacklist_submission(
        self,
        modal: CustomModal,
        ctx: discord.Interaction['Bot'],
        target_user: Optional[discord.User | discord.Member],
        target_server: discord.Guild,
    ):
        """Handle the submission of the blacklist modal."""
        reason = modal.saved_items['reason'].value
        duration_str = (
            modal.saved_items['duration'].value.strip()
            if modal.saved_items['duration'].value
            else ''
        )

        duration_ms = parse_duration(duration_str) if duration_str else None
        expires_at = duration_to_datetime(duration_ms)

        if target_user:
            # User blacklist
            await blacklist_user(
                db=self.bot.db,
                user_id=str(target_user.id),
                mod_id=str(self.moderator.id),
                reason=reason,
                duration=duration_ms,
            )
            target_name = target_user.display_name
            target_type = 'User'
        elif target_server:
            # Server blacklist
            await blacklist_server(
                db=self.bot.db,
                server_id=str(target_server.id),
                mod_id=str(self.moderator.id),
                reason=reason,
                duration=duration_ms,
            )
            target_name = target_server.name
            target_type = 'Server'
        else:
            await ctx.followup.send(
                f'{self.bot.emotes.x_icon} No valid target provided.',
                ephemeral=True,
            )
            return

        embed = discord.Embed(
            title=f'{self.bot.emotes.ban_icon} {target_type} Blacklisted',
            description=f'Successfully blacklisted {target_type.lower()} **{target_name}**',
            color=discord.Color.dark_red(),
        )
        embed.add_field(name='Reason', value=reason, inline=False)
        embed.add_field(name='Moderator', value=self.moderator.mention, inline=True)

        if expires_at:
            embed.add_field(name='Duration', value=duration_str, inline=True)
            embed.set_footer(text=f'Expires: {expires_at.strftime("%Y-%m-%d %H:%M:%S")}')
        else:
            embed.add_field(name='Duration', value='Permanent', inline=True)

        await ctx.followup.send(embed=embed, ephemeral=True)
