from collections.abc import Sequence
import aiohttp
import discord
from discord.ext import commands
from typing import TYPE_CHECKING, Optional
from sqlalchemy import select
from utils.modules.core.db.models import Connection, Hub
from utils.constants import logger

from utils.modules.broadcast.messageUtils import (
    create_reply_embed,
    format_message_with_attachments,
    store_message_and_broadcasts,
    get_original_message_author,
    format_user_badges,
    create_allowed_mentions_for_broadcast,
)
from utils.utils import upsert_user
from utils.modules.broadcast.checks import MessageValidator
from utils.modules.broadcast.notify import NotificationManager
from utils.modules.core.antiSpam import AntiSpamManager

if TYPE_CHECKING:
    from main import Bot


class OnMessage(commands.Cog):
    def __init__(self, bot):
        self.bot: 'Bot' = bot
        self._http_session = aiohttp.ClientSession()

    async def cog_unload(self):
        await self._http_session.close()

    @commands.Cog.listener()
    async def on_message(self, message: discord.Message):
        if message.author.bot or message.author.system or not message.guild:
            return

        # check db to see if message was sent in connected channel or not
        async with self.bot.db.get_session() as session:
            stmt = (
                select(Connection, Hub)
                .join(Hub, Connection.hubId == Hub.id)
                .where(
                    Connection.channelId == str(message.channel.id),
                    Connection.connected,
                )
            )

            result = await session.execute(stmt)
            row = result.first()

            if row is None:
                return

            connection, hub = row
            logger.debug(f'Message from {message.author}: {message.content}')

            await upsert_user(message.author, session)

            # Perform validation checks before processing message
            validator = MessageValidator(session)
            guild_id = str(message.guild.id)
            validation_result = await validator.validate_message(
                message, hub, str(message.author.id), guild_id
            )

            # If validation failed, handle notification and spam actions
            if not validation_result.is_valid:
                # Handle spam-specific actions
                if validation_result.spam_action:
                    spam_manager = AntiSpamManager(session)

                    if validation_result.spam_action == 'mute':
                        # Mute the user for spam
                        infraction_id = await spam_manager.mute_user_for_spam(
                            self.bot, str(message.author.id), hub.id, message.author.name
                        )
                        if infraction_id:
                            # Update notification message with infraction ID for tracking
                            validation_result = validation_result._replace(
                                infraction_id=infraction_id
                            )

                # Send notification if needed
                if validation_result.should_notify and validation_result.notification_message:
                    notifier = NotificationManager(session)
                    await notifier.send_validation_notification(
                        message.author,
                        validation_result.notification_message,
                        validation_result.infraction_id,
                    )
                logger.info(f'Message blocked: {validation_result.reason}')
                return

            processed_content = format_message_with_attachments(
                message.content, message.attachments, message.stickers
            )

            # Create reply embed only if this is a reply
            reply_embed = await create_reply_embed(message.reference, session)

            # Get all other connections in the same hub (excluding current channel)
            # This query is optimized to use the Connection_hubId_connected_idx index
            other_connections_stmt = select(Connection).where(
                Connection.hubId == hub.id,
                Connection.connected,  # Check this first for better index usage
                Connection.channelId != str(message.channel.id),
            )
            result = await session.execute(other_connections_stmt)
            other_connections = result.scalars().all()

            if not other_connections:
                logger.debug(f'No other connections found for hub {hub.id}')
                return

            try:
                # Batch database operations to reduce query count
                # Get reply data for smart mentions and user badges in parallel if possible
                reply_data = None
                if message.reference and message.reference.message_id:
                    # Only fetch reply data if this is actually a reply
                    reply_data = await get_original_message_author(
                        str(message.reference.message_id), session
                    )

                # Get user badges for display (this is already optimized to select only needed fields)
                badge_prefix = await format_user_badges(self.bot, str(message.author.id), session)

                # Store original message and broadcast to other connections
                broadcast_message_ids = await self.broadcast_message(
                    message,
                    connection,
                    hub,
                    other_connections,
                    processed_content,
                    reply_embed,
                    badge_prefix,
                    reply_data,
                )

                # Store message and broadcast records in database
                await store_message_and_broadcasts(
                    message, hub, processed_content, broadcast_message_ids, session
                )

            except Exception as e:
                logger.error(f'Error broadcasting message: {e}')

    async def broadcast_message(
        self,
        message: discord.Message,
        connection: Connection,
        hub: Hub,
        other_connections: Sequence[Connection],
        processed_content: str,
        reply_embed: Optional[discord.Embed],
        badge_prefix: str = '',
        reply_data: Optional[tuple] = None,
    ) -> list[tuple[str, str]]:
        """Broadcast message to other connections and return broadcast results"""
        broadcast_message_ids = []

        # Extract reply information for smart mentions
        replied_user_id = None
        original_server_id = None
        if reply_data:
            _, replied_user, original_server_id = reply_data
            replied_user_id = replied_user.id

        for other_conn in other_connections:
            # Check if the webhook is already cached
            webhook = discord.Webhook.from_url(
                url=other_conn.webhookURL, session=self._http_session
            )

            # Prepare embeds list
            embeds = [reply_embed] if reply_embed else []

            # Format final message content with badges
            final_content = badge_prefix + processed_content

            # Create allowed mentions for this specific broadcast
            allowed_mentions = create_allowed_mentions_for_broadcast(
                replied_user_id, other_conn.channelId, original_server_id
            )

            # If the connection has a parentId, it means it's a thread
            if other_conn.parentId:
                sent_message = await webhook.send(
                    final_content,
                    username=f'{message.author.name} | {getattr(message.guild, "name", "Unknown Server")}',
                    avatar_url=message.author.display_avatar.url,
                    thread=discord.Object(id=other_conn.channelId),
                    embeds=embeds,
                    allowed_mentions=allowed_mentions,
                    wait=True,
                )
            else:
                # Send the message using the webhook
                sent_message = await webhook.send(
                    final_content,
                    username=f'{message.author.name} | {getattr(message.guild, "name", "Unknown Server")}',
                    avatar_url=message.author.display_avatar.url,
                    embeds=embeds,
                    allowed_mentions=allowed_mentions,
                    wait=True,
                )

            # Store the broadcast message ID and channel ID
            if sent_message:
                broadcast_message_ids.append((str(sent_message.id), other_conn.channelId))

        logger.debug(
            f'Broadcasted message from {message.author} to {len(other_connections)} channels in hub {hub.id}'
        )

        return broadcast_message_ids


async def setup(bot):
    await bot.add_cog(OnMessage(bot))
