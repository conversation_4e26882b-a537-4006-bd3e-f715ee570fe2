from typing import TYPE_CHECKING

from utils.modules.hub.hubLogging import log_event
from utils.modules.events.baseEventCog import BaseEventCog
from utils.modules.events.eventDispatcher import (
    HubEvent,
    HubEventType,
)
from utils.constants import InterchatConstants
from utils.modules.events.eventDecorator import hub_event_listener

if TYPE_CHECKING:
    from main import Bot


class HubMessageEvents(BaseEventCog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = InterchatConstants()
        super().__init__(bot)

    @hub_event_listener(HubEventType.MESSAGE_EDIT)
    async def on_message_edit(self, event: HubEvent):
        """Handle message edit events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.MESSAGE_DELETE)
    async def on_message_delete(self, event: <PERSON>b<PERSON>vent):
        """Handle message deletion events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.MESSAGE_REPORT)
    async def on_message_report(self, event: HubEvent):
        """Handle message report events."""
        await log_event(self.bot, event)


async def setup(bot):
    await bot.add_cog(HubMessageEvents(bot))
