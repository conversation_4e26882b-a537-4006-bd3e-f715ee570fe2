from typing import TYPE_CHECKING

from utils.modules.hub.hubLogging import log_event
from utils.modules.events.baseEventCog import BaseEventCog
from utils.modules.events.eventDispatcher import (
    HubEvent,
    HubEventType,
)
from utils.constants import InterchatConstants
from utils.modules.events.eventDecorator import hub_event_listener

if TYPE_CHECKING:
    from main import Bot


class HubManagementEvents(BaseEventCog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = InterchatConstants()
        super().__init__(bot)

    @hub_event_listener(HubEventType.HUB_CREATE)
    async def on_hub_create(self, event: HubEvent):
        """Handle hub creation events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.HUB_DELETE)
    async def on_hub_delete(self, event: HubEvent):
        """Handle hub deletion events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.HUB_UPDATE)
    async def on_hub_update(self, event: <PERSON>b<PERSON>vent):
        """Handle hub update events."""
        await log_event(self.bot, event)


async def setup(bot):
    await bot.add_cog(HubManagementEvents(bot))
