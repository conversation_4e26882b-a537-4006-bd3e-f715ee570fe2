from typing import TYPE_CHECKING

from utils.modules.hub.hubLogging import log_event
from utils.modules.events.baseEventCog import BaseEventCog
from utils.modules.events.eventDispatcher import (
    HubEvent,
    HubEventType,
)
from utils.constants import InterchatConstants
from utils.modules.events.eventDecorator import hub_event_listener

if TYPE_CHECKING:
    from main import Bot


class ConnectionEvents(BaseEventCog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = InterchatConstants()
        super().__init__(bot)

    @hub_event_listener(HubEventType.CONNECTION_ADD)
    async def on_connection_add(self, event: HubEvent):
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.CONNECTION_REMOVE)
    async def on_connection_remove(self, event: HubEvent):
        """Handle server disconnection events."""
        await log_event(self.bot, event)


async def setup(bot):
    await bot.add_cog(ConnectionEvents(bot))
