import discord
from discord.ext import commands

from utils.constants import InterchatConstants


class OnGuildJoin(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.constants = InterchatConstants()

    @commands.Cog.listener()
    async def on_guild_join(self, guild: discord.Guild):
        embed = discord.Embed(
            title='👋 Hey there!',
            description=f"**I'm InterChat, and I'm pleased to be on your server on behalf of our team.**"
            f'Together, we can connect your server with other remarkable communities from across Discord. Numerous, potentially thousands of servers located in one place, all filled with individuals eager to engage in conversation with you.'
            f'🚀 **Ready to start building new bridges with us?**'
            f'\n{self.bot.emotes.dot} **New here?** The `/setup` command provides a step-by-step guide to help you begin your journey.'
            f'\n{self.bot.emotes.dot} **Ready to explore?** Visit our [discovery page](https://interchat.tech/hubs) to locate and become part of our lively hubs.'
            f"\n{self.bot.emotes.dot} **Perhaps something you're more familiar with?** Try our `/call` for one on one connections."
            f"\n💝 **Lost? Require assistance?** We welcome you to our [support community](https://discord.gg/8DhUA4HNpD). Our team and our community will be pleased to offer their help. We offer as much support and assistance in whatever issues you may have, don't hesitate to join!",
            color=self.constants.color(),
        )

        for channel in sorted(guild.text_channels, key=lambda x: x.position):
            everyone = channel.permissions_for(guild.default_role)
            bot = channel.permissions_for(guild.me)

            if everyone.send_messages and bot.send_messages:
                await channel.send(embed=embed)
                return


async def setup(bot):
    await bot.add_cog(OnGuildJoin(bot))
