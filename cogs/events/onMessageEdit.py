import asyncio
import aiohttp
import discord
from discord.ext import commands
from sqlalchemy import select, update
from typing import TYPE_CHECKING, Dict, Optional

from utils.modules.core.db.models import Broadcast, Connection, Message
from utils.constants import logger

if TYPE_CHECKING:
    from main import Bot


class onMessageEdit(commands.Cog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot

        self._webhook_cache: Dict[str, Optional[discord.Webhook]] = {}
        self._cache_lock = asyncio.Lock()
        self._edit_queue: asyncio.Queue[tuple[str, int, str]] = asyncio.Queue()
        self._http_session = aiohttp.ClientSession()
        self._worker_task = bot.loop.create_task(self._edit_worker())

    async def cog_unload(self):
        self._webhook_cache.clear()
        self._worker_task.cancel()
        await self._http_session.close()

    @commands.Cog.listener()
    async def on_message_edit(self, before: discord.Message, after: discord.Message):
        if before.author.bot or before.content == after.content:
            return

        async with self.bot.db.get_session() as db:
            result = await db.execute(
                select(Broadcast.channelId, Broadcast.id).where(
                    Broadcast.messageId == str(before.id)
                )
            )
            records = result.all()

            if not records:
                return

            # Queue all records at once
            for rec in records:
                self._edit_queue.put_nowait((rec.channelId, int(rec.id), after.content))

            # Update database record
            stmt = update(Message).where(Message.id == str(before.id)).values(content=after.content)

            await db.execute(stmt)
            await db.commit()
            logger.debug(f'Queued and updated {len(records)} broadcast record(s)')

    async def _get_webhook(self, channel_id: str) -> Optional[discord.Webhook]:
        """Get webhook from cache or database with locking."""
        async with self._cache_lock:
            if channel_id in self._webhook_cache:
                return self._webhook_cache[channel_id]

            # Fetch from database if not cached
            async with self.bot.db.get_session() as session:
                webhook_url = await session.scalar(
                    select(Connection.webhookURL).where(Connection.channelId == channel_id)
                )
            webhook = None
            if webhook_url:
                webhook = discord.Webhook.from_url(webhook_url, session=self._http_session)
                self._webhook_cache[channel_id] = webhook

            return webhook

    async def _edit_worker(self):
        while True:
            try:
                channel_id, webhook_msg_id, new_content = await self._edit_queue.get()
                try:
                    webhook = await self._get_webhook(channel_id)
                    if not webhook:
                        logger.warning(f'No webhook found for channel {channel_id}')
                        continue

                    # Edit the message using the webhook
                    await webhook.edit_message(webhook_msg_id, content=new_content)
                    logger.debug(f'Edited webhook msg {webhook_msg_id} in channel {channel_id}')
                except discord.NotFound:
                    logger.warning(f'Message {webhook_msg_id} not found in channel {channel_id}')
                    # Clear cache if webhook is invalid
                    async with self._cache_lock:
                        self._webhook_cache.pop(channel_id, None)

                    logger.warning(
                        f'Webhook message {webhook_msg_id} not found in channel {channel_id}'
                    )
                finally:
                    self._edit_queue.task_done()

            except asyncio.CancelledError:
                break


async def setup(bot: 'Bot'):
    await bot.add_cog(onMessageEdit(bot))
