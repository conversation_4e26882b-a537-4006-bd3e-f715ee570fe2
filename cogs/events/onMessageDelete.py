import asyncio
import aiohttp
import discord
from discord.ext import commands
from sqlalchemy import select, delete
from typing import TYPE_CHECKING, Dict, Optional

from utils.modules.core.db.models import Broadcast, Connection
from utils.constants import logger

if TYPE_CHECKING:
    from main import Bot


class onMessageDelete(commands.Cog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self._http_session = aiohttp.ClientSession()
        # Cache for webhook to reduce database queries
        self._webhook_cache: Dict[str, Optional[discord.Webhook]] = {}
        self._cache_lock = asyncio.Lock()
        self._delete_queue: asyncio.Queue[tuple[str, str]] = asyncio.Queue()
        self._worker_task = bot.loop.create_task(self._delete_worker())

    async def cog_unload(self):
        self._worker_task.cancel()
        self._webhook_cache.clear()
        await self._http_session.close()

    @commands.Cog.listener()
    async def on_message_delete(self, message: discord.Message):
        if message.author.bot:
            return

        async with self.bot.db.get_session() as session:
            # Fetch only necessary columns (channelId and id)
            result = await session.execute(
                select(Broadcast.channelId, Broadcast.id).where(
                    Broadcast.messageId == str(message.id)
                )
            )
            records = result.all()

            if not records:
                return

            for rec in records:
                self._delete_queue.put_nowait((rec.channelId, rec.id))

            # Bulk delete database records
            await session.execute(delete(Broadcast).where(Broadcast.messageId == str(message.id)))
            await session.commit()
            logger.debug(f'Queued and removed {len(records)} broadcast record(s)')

    async def _get_webhook(self, channel_id: str) -> Optional[discord.Webhook]:
        """Get webhook from cache or database with locking."""
        async with self._cache_lock:
            if channel_id in self._webhook_cache:
                return self._webhook_cache[channel_id]

            # Fetch from database if not cached
            async with self.bot.db.get_session() as session:
                webhook_url = await session.scalar(
                    select(Connection.webhookURL).where(Connection.channelId == channel_id)
                )
            webhook = None
            if webhook_url:
                webhook = discord.Webhook.from_url(webhook_url, session=self._http_session)
                self._webhook_cache[channel_id] = webhook

            return webhook

    async def _delete_worker(self):
        while True:
            try:
                channel_id, msg_id = await self._delete_queue.get()

                try:
                    webhook = await self._get_webhook(channel_id)

                    if not webhook:
                        logger.warning(f'No webhook for channel {channel_id}')
                        continue

                    try:
                        await webhook.delete_message(int(msg_id))
                        logger.debug(f'Deleted webhook message {msg_id} in channel {channel_id}')
                    except discord.NotFound:
                        logger.warning(f'Message {msg_id} not found in channel {channel_id}')
                        # Clear cache if webhook is invalid
                        async with self._cache_lock:
                            self._webhook_cache.pop(channel_id, None)
                except discord.NotFound as e:
                    logger.warning(f'Webhook for channel {channel_id} not found: {e}')
                except Exception as e:
                    logger.error(f'Error deleting message {msg_id} in channel {channel_id}: {e}')
                finally:
                    # Always mark task as done since we successfully got it from queue
                    self._delete_queue.task_done()

            except asyncio.CancelledError:
                break


async def setup(bot: 'Bot'):
    await bot.add_cog(onMessageDelete(bot))
