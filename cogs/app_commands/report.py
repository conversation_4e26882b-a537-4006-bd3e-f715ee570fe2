import discord
from discord.ext import commands
from discord import app_commands
from discord.ui import View, button, Button

from sqlalchemy import select
from typing import TYPE_CHECKING
from datetime import datetime, timedelta

from utils.modules.core.db.models import Message
from utils.constants import InterchatConstants

if TYPE_CHECKING:
    from main import Bot


class ScopeView(View):
    def __init__(self, bot, user, constants, message):
        super().__init__(timeout=120)
        self.bot: 'Bot' = bot
        self.user: discord.User | discord.Member = user
        self.constants = constants
        self.message: discord.Message = message

        self.setup_buttons()

    def setup_buttons(self):
        self.global_callback.emoji = self.bot.emotes.globe_icon
        self.global_callback.disabled = False

        self.hub_callback.emoji = self.bot.emotes.house_icon
        self.hub_callback.disabled = False

    async def _fetch_messages(self):
        async with self.bot.db.get_session() as session:
            ten_minutes_ago = datetime.now() - timedelta(minutes=10)

            stmt = (
                select(Message)
                .where(
                    Message.channelId == str(self.message.channel.id),
                    Message.createdAt >= ten_minutes_ago,
                )
                .order_by(Message.createdAt.desc())
            )

            result = await session.execute(stmt)
            messages = result.scalars().all()
            
            if not messages:
                stmt = (select(Message).where(Message.channelId == str(self.message.channel.id)).order_by(Message.createdAt.desc()).limit(50))
                
                result = await session.execute(stmt)
                messages = result.scalars().all()
            
            return messages

    @button(
        label='Report to InterStaff',
        style=discord.ButtonStyle.grey,
        disabled=True
    )
    async def global_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()

    @button(
        label='Report to hub staff',
        style=discord.ButtonStyle.grey,
        disabled=True
    )
    async def hub_callback(self, interaction: discord.Interaction['Bot'], button: Button):
        await interaction.response.defer()

class Report(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.constants = InterchatConstants()

        self.report_message_menu = app_commands.ContextMenu(
            name='Report Message', callback=self.report_message
        )

        self.bot.tree.add_command(self.report_message_menu)

    async def cog_unload(self):
        self.bot.tree.remove_command(
            self.report_message_menu.name, type=discord.AppCommandType.message
        )

    async def report_message(self, interaction: discord.Interaction, message: discord.Message):
        await interaction.response.defer(ephemeral=True)

        if message.author == interaction.user:
            embed = discord.Embed(
                title='Error!',
                description=f'{self.bot.emotes.x_icon} You may not report your own messages.',
                color=self.constants.color(),
            )
            return await interaction.followup.send(embed=embed, ephemeral=True)

        async with self.bot.db.get_session() as session:
            stmt = select(Message).where(Message.id == str(message.id))
            result = (await session.execute(stmt)).scalar_one_or_none()

        if not result:
            embed = discord.Embed(
                title='Error!',
                description=f'{self.bot.emotes.x_icon} You may only report a message sent within a hub.',
                color=self.constants.color(),
            )
            return await interaction.followup.send(embed=embed, ephemeral=True)

        embed = discord.Embed(
            title='Report User',
            description=f"Report {message.author.mention} to **Hub Staff** for hub violations or **InterChat Staff** for platform violations. The user won't be notified, but moderators can see who has submit the report.",
            color=self.constants.color(),
        )
        embed.set_author(name=f'@{interaction.user}', icon_url=interaction.user.display_avatar.url)
        embed.set_footer(
            text='The last 10 minutes of your conversation will be sent to the selected party.'
        )
        view = ScopeView(self.bot, interaction.user, self.constants, message)
        await interaction.followup.send(embed=embed, view=view, ephemeral=True)


async def setup(bot):
    await bot.add_cog(Report(bot))
