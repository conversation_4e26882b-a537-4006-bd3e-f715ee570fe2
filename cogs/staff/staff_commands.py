import json
import discord
from discord.ext import commands
from sqlalchemy import select
from typing import TYPE_CHECKING, Optional
from datetime import datetime

from utils.modules.core.checks import is_interchat_staff_check
from utils.modules.core.db.models import <PERSON><PERSON>, User, ServerData
from utils.constants import InterchatConstants
from utils.modules.core.moderation import (
    blacklist_server,
    blacklist_user,
    unblacklist_server,
    unblacklist_user,
)
from utils.utils import parse_duration

if TYPE_CHECKING:
    from main import Bot


class Staff(commands.Cog):
    """Staff commands for InterChat administration and moderation."""

    def __init__(self, bot: 'Bot') -> None:
        """Initialize the Staff cog.

        Args:
            bot: The bot instance.
        """
        self.bot: 'Bot' = bot
        self.constants = InterchatConstants()

    @commands.group(name='get', description='Get information about InterChat entities')
    async def get_group(self, ctx: commands.Context) -> None:
        """Group command for retrieving information about hubs, servers, and users."""
        if ctx.invoked_subcommand is None:
            await ctx.send_help(ctx.command)

    def _format_timestamp(self, timestamp: datetime) -> str:
        """Format a datetime object for Discord timestamp display."""
        unix_timestamp = int(timestamp.timestamp())
        return f'<t:{unix_timestamp}:D>\n<t:{unix_timestamp}:R>'

    def _create_base_embed(self, title: Optional[str] = None) -> discord.Embed:
        """Create a base embed with consistent styling."""
        embed = discord.Embed(color=self.constants.color())
        if title:
            embed.title = title
        return embed

    def _add_status_badges(self, embed: discord.Embed, hub: Hub) -> None:
        """Add status badges to a hub embed.

        Args:
            embed: The embed to add badges to.
            hub: The hub object containing status information.
        """
        status_badges = []
        if hub.verified:
            status_badges.append(f'{self.bot.emotes.tick} Verified')
        if hub.partnered:
            status_badges.append(f'{self.bot.emotes.connect} Partnered')
        if hub.featured:
            status_badges.append(f'{self.bot.emotes.star} Featured')
        if hub.private:
            status_badges.append(f'{self.bot.emotes.lock_icon} Private')
        if hub.locked:
            status_badges.append(f'{self.bot.emotes.no} Locked')
        if hub.nsfw:
            status_badges.append(f'{self.bot.emotes.alert_icon} NSFW')

        if status_badges:
            embed.add_field(name='Status', value=' • '.join(status_badges), inline=False)

    def _add_moderation_summary(self, embed: discord.Embed, hub: Hub) -> None:
        """Add moderation summary to a hub embed.

        Args:
            embed: The embed to add moderation info to.
            hub: The hub object containing moderation information.
        """
        if not any([hub.moderators, hub.blockWords, hub.antiSwearRules, hub.rules]):
            return

        mod_summary = []
        if hub.moderators:
            mod_summary.append(f'{len(hub.moderators)} mods')
        if hub.rules:
            mod_summary.append(f'{len(hub.rules)} rules')
        if hub.blockWords:
            mod_summary.append(f'{len(hub.blockWords)} blocked words')
        if hub.antiSwearRules:
            mod_summary.append(f'{len(hub.antiSwearRules)} anti-swear rules')

        embed.add_field(
            name=f'{self.bot.emotes.hammer_icon} Moderation',
            value=' • '.join(mod_summary),
            inline=False,
        )

    @get_group.command(name='hub', description='Get detailed information about a hub')
    @is_interchat_staff_check()
    async def get_hub(self, ctx: commands.Context, hub_name: str) -> None:
        """Retrieve and display detailed information about a specific hub."""
        hub_data = await self._fetch_hub_by_name(ctx, hub_name)
        if not hub_data:
            await ctx.send('Hub not found.')
            return

        embed = await self._create_hub_embed(hub_data)
        await ctx.send(embed=embed)

    async def _fetch_hub_by_name(self, ctx: commands.Context, hub_name: str) -> Optional[Hub]:
        """Fetch a hub from the database by name."""
        async with ctx.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.name == hub_name)
            result = await session.execute(stmt)
            return result.scalar()

    async def _create_hub_embed(self, hub: Hub) -> discord.Embed:
        """Create a detailed embed for a hub."""
        embed = self._create_base_embed()

        # Set thumbnail if available
        if hub.iconUrl and hub.iconUrl != 'none':
            embed.set_thumbnail(url=hub.iconUrl)
        elif hub.bannerUrl and hub.bannerUrl != 'none':
            embed.set_image(url=hub.bannerUrl)

        # Basic information
        hub_title = f'**{hub.name}**'
        if hub.shortDescription:
            hub_title += f'\n{hub.shortDescription}'
        embed.add_field(name=f'{self.bot.emotes.hash_icon} Name', value=hub_title, inline=True)

        # Statistics
        embed.add_field(
            name=f'{self.bot.emotes.chat_icon} Messages',
            value=f'**{hub.weeklyMessageCount}** this week',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.link_icon} Connections',
            value=f'**{len(hub.connections)}** active',
            inline=True,
        )

        # Timestamps
        embed.add_field(
            name=f'{self.bot.emotes.globe_icon} Created',
            value=self._format_timestamp(hub.createdAt),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.refresh_icon} Last Active',
            value=self._format_timestamp(hub.lastActive),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.trophy_icon} Upvotes',
            value=f'**{len(hub.upvotes)}** total',
            inline=True,
        )

        # Owner and location
        embed.add_field(
            name=f'{self.bot.emotes.person_icon} Owner',
            value=f'<@{hub.ownerId}>',
            inline=True,
        )

        location_info = []
        if hub.language:
            location_info.append(f'{self.bot.emotes.globe_icon} {hub.language}')
        if hub.region:
            location_info.append(f'{self.bot.emotes.house_icon} {hub.region}')

        location_value = ' • '.join(location_info) if location_info else 'Not specified'
        embed.add_field(
            name=f'{self.bot.emotes.globe_icon} Location',
            value=location_value,
            inline=True,
        )

        embed.add_field(
            name=f'{self.bot.emotes.fire_icon} Activity',
            value=hub.activityLevel.value.title(),
            inline=True,
        )

        # Status badges and moderation
        self._add_status_badges(embed, hub)
        self._add_moderation_summary(embed, hub)

        # Additional information
        if hub.appealCooldownHours != 168:
            embed.add_field(
                name=f'{self.bot.emotes.clock_icon} Appeal Cooldown',
                value=f'{hub.appealCooldownHours} hours',
                inline=True,
            )

        if hub.welcomeMessage:
            welcome_preview = (
                hub.welcomeMessage[:150] + '...'
                if len(hub.welcomeMessage) > 150
                else hub.welcomeMessage
            )
            embed.add_field(
                name=f'{self.bot.emotes.wave_anim} Welcome Message',
                value=f'```{welcome_preview}```',
                inline=False,
            )

        if (
            hub.description
            and hub.description != hub.shortDescription
            and len(hub.description) > 100
        ):
            desc_preview = (
                hub.description[:200] + '...' if len(hub.description) > 200 else hub.description
            )
            embed.add_field(
                name=f'{self.bot.emotes.description} Description',
                value=desc_preview,
                inline=False,
            )

        if hub.bannerUrl:
            embed.set_image(url=hub.bannerUrl)

        embed.set_footer(text=f'Hub ID: {hub.id}')
        return embed

    @get_group.command(name='server', description='Get detailed information about a server')
    @is_interchat_staff_check()
    async def get_server(self, ctx: commands.Context, server_id: str) -> None:
        """Retrieve and display detailed information about a specific server."""
        server_data = await self._fetch_server_by_id(ctx, server_id)
        if not server_data:
            await ctx.send('Server not found.')
            return

        embed = await self._create_server_embed(server_data)
        await ctx.send(embed=embed)

    async def _fetch_server_by_id(
        self, ctx: commands.Context, server_id: str
    ) -> Optional[ServerData]:
        """Fetch a server from the database by ID."""
        async with ctx.bot.db.get_session() as session:
            stmt = select(ServerData).where(ServerData.id == server_id)
            result = await session.execute(stmt)
            return result.scalar()

    async def _create_server_embed(self, server: ServerData) -> discord.Embed:
        """Create a detailed embed for a server."""
        embed = self._create_base_embed()

        # Set thumbnail if available
        if server.iconUrl:
            embed.set_thumbnail(url=server.iconUrl)

        # Basic information
        embed.add_field(
            name=f'{self.bot.emotes.hash_icon} Name',
            value=f'**{server.name}**',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.ID_icon} Server ID',
            value=f'`{server.id}`',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.invite_icon} Invite Code',
            value=f'`{server.inviteCode}`',
            inline=True,
        )

        # Statistics
        embed.add_field(
            name=f'{self.bot.emotes.chat_icon} Messages',
            value=f'**{server.messageCount}** total',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.link_icon} Connections',
            value=f'**{len(server.connections)}** active',
            inline=True,
        )

        # Status
        premium_status = (
            f'{self.bot.emotes.globe_icon} Premium'
            if server.premiumStatus
            else f'{self.bot.emotes.dot} Standard'
        )
        embed.add_field(
            name=f'{self.bot.emotes.trophy_icon} Status',
            value=premium_status,
            inline=True,
        )

        # Timestamps
        embed.add_field(
            name=f'{self.bot.emotes.calendar_icon} Created',
            value=self._format_timestamp(server.createdAt),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.clock_icon} Last Message',
            value=self._format_timestamp(server.lastMessageAt),
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.refresh_icon} Updated',
            value=self._format_timestamp(server.updatedAt),
            inline=True,
        )

        # Moderation summary
        mod_summary = []
        if server.infractions:
            mod_summary.append(f'{len(server.infractions)} infractions')
        if server.serverBlacklists:
            mod_summary.append(f'{len(server.serverBlacklists)} blacklists')

        if mod_summary:
            embed.add_field(name='Moderation', value=' • '.join(mod_summary), inline=False)

        # Leaderboard entries
        if server.leaderboardEntries:
            embed.add_field(
                name=f'{self.bot.emotes.leaderboard_icon} Leaderboard',
                value=f'**{len(server.leaderboardEntries)}** entries',
                inline=True,
            )

        embed.set_footer(text=f'Server ID: {server.id}')
        return embed

    @get_group.command(name='user', description='Get detailed information about a user')
    @is_interchat_staff_check()
    async def get_user(self, ctx: commands.Context, user: discord.User) -> None:
        """Retrieve and display detailed information about a specific user."""
        user_data = await self._fetch_user_by_id(ctx, str(user.id))
        if not user_data:
            await ctx.send('User not found.')
            return

        embed = await self._create_user_embed(user_data)
        await ctx.send(embed=embed)

    async def _fetch_user_by_id(self, ctx: commands.Context, user_id: str) -> Optional[User]:
        """Fetch a user from the database by ID."""
        async with ctx.bot.db.get_session() as session:
            stmt = select(User).where(User.id == user_id)
            result = await session.execute(stmt)
            return result.scalar()

    async def _create_user_embed(self, user: User) -> discord.Embed:
        """Create a detailed embed for a user."""
        embed = self._create_base_embed()

        # Set thumbnail if available
        if user.image:
            embed.set_thumbnail(url=user.image)

        # Basic information
        embed.add_field(
            name=f'{self.bot.emotes.person_icon} Name',
            value=f'**{user.name}**',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.ID_icon} User ID', value=f'`{user.id}`', inline=True
        )

        # Status
        staff_status = (
            f'{self.bot.emotes.staff_badge} Staff'
            if user.isStaff
            else f'{self.bot.emotes.dot} Member'
        )
        embed.add_field(name=f'{self.bot.emotes.info_icon} Status', value=staff_status, inline=True)

        # Statistics
        self._add_user_statistics(embed, user)

        # Timestamps
        self._add_user_timestamps(embed, user)

        # Additional sections
        self._add_user_preferences(embed, user)
        self._add_user_owned_content(embed, user)
        self._add_user_moderation_summary(embed, user)
        self._add_user_activity_summary(embed, user)
        self._add_user_donation_info(embed, user)
        self._add_user_badges(embed, user)

        embed.set_footer(text=f'User ID: {user.id}')
        return embed

    def _add_user_statistics(self, embed: discord.Embed, user: User) -> None:
        """Add user statistics to the embed."""
        embed.add_field(
            name=f'{self.bot.emotes.chat_icon} Messages',
            value=f'**{user.messageCount}** sent',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.trophy_icon} Reputation',
            value=f'**{user.reputation}** points',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.globe_icon} Votes',
            value=f'**{user.voteCount}** cast',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.link_icon} Hub Joins',
            value=f'**{user.hubJoinCount}** total',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.fire_icon} Engagement',
            value=f'**{user.hubEngagementScore:.1f}**',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.globe_icon} Locale',
            value=f'**{user.locale.upper() if user.locale else "Not set"}**',
            inline=True,
        )

    def _add_user_timestamps(self, embed: discord.Embed, user: User) -> None:
        """Add user timestamps to the embed."""
        if user.createdAt:
            embed.add_field(
                name=f'{self.bot.emotes.calendar_icon} Created',
                value=self._format_timestamp(user.createdAt),
                inline=True,
            )

        if user.lastMessageAt:
            embed.add_field(
                name=f'{self.bot.emotes.clock_icon} Last Message',
                value=self._format_timestamp(user.lastMessageAt),
                inline=True,
            )

        if user.lastVoted:
            embed.add_field(
                name=f'{self.bot.emotes.globe_icon} Last Vote',
                value=self._format_timestamp(user.lastVoted),
                inline=True,
            )
        else:
            embed.add_field(
                name=f'{self.bot.emotes.globe_icon} Last Vote',
                value='Never',
                inline=True,
            )

    def _add_user_preferences(self, embed: discord.Embed, user: User) -> None:
        """Add user preferences to the embed."""
        preferences = []
        if user.showBadges:
            preferences.append('Show badges')
        if user.mentionOnReply:
            preferences.append('Mention on reply')
        if user.showNsfwHubs:
            preferences.append('Show NSFW hubs')

        if preferences:
            embed.add_field(name='Preferences', value=' • '.join(preferences), inline=False)

    def _add_user_owned_content(self, embed: discord.Embed, user: User) -> None:
        """Add user owned content summary to the embed."""
        owned_summary = []
        if user.ownedHubs:
            owned_summary.append(f'{len(user.ownedHubs)} hubs')
        if user.modPositions:
            owned_summary.append(f'{len(user.modPositions)} mod positions')
        if user.blockWordsCreated:
            owned_summary.append(f'{len(user.blockWordsCreated)} blocked words')
        if user.antiSwearRulesCreated:
            owned_summary.append(f'{len(user.antiSwearRulesCreated)} anti-swear rules')

        if owned_summary:
            embed.add_field(name='Created Content', value=' • '.join(owned_summary), inline=False)

    def _add_user_moderation_summary(self, embed: discord.Embed, user: User) -> None:
        """Add user moderation summary to the embed."""
        mod_summary = []
        if user.infractions:
            mod_summary.append(f'{len(user.infractions)} infractions received')
        if user.issuedInfractions:
            mod_summary.append(f'{len(user.issuedInfractions)} infractions issued')
        if user.blacklists:
            mod_summary.append(f'{len(user.blacklists)} blacklists received')
        if user.issuedBlacklists:
            mod_summary.append(f'{len(user.issuedBlacklists)} blacklists issued')

        if mod_summary:
            embed.add_field(
                name=f'{self.bot.emotes.hammer_icon} Moderation',
                value=' • '.join(mod_summary),
                inline=False,
            )

    def _add_user_activity_summary(self, embed: discord.Embed, user: User) -> None:
        """Add user activity summary to the embed."""
        activity_summary = []
        if user.appeals:
            activity_summary.append(f'{len(user.appeals)} appeals')
        if user.reviews:
            activity_summary.append(f'{len(user.reviews)} reviews')
        if user.reportsSubmitted:
            activity_summary.append(f'{len(user.reportsSubmitted)} reports made')
        if user.reportsReceived:
            activity_summary.append(f'{len(user.reportsReceived)} reports received')
        if user.achievements:
            activity_summary.append(f'{len(user.achievements)} achievements')

        if activity_summary:
            embed.add_field(
                name=f'{self.bot.emotes.activities} Activity',
                value=' • '.join(activity_summary),
                inline=False,
            )

    def _add_user_donation_info(self, embed: discord.Embed, user: User) -> None:
        """Add user donation information to the embed."""
        if user.donationTier:
            donation_text = f'Tier: {user.donationTier}'
            if user.donationExpiresAt:
                donation_text += f'\nExpires: <t:{int(user.donationExpiresAt.timestamp())}:R>'
            embed.add_field(
                name=f'{self.bot.emotes.globe_icon} Donation',
                value=donation_text,
                inline=True,
            )

    def _add_user_badges(self, embed: discord.Embed, user: User) -> None:
        """Add user badges to the embed."""
        if user.badges:
            badge_text = ' • '.join([badge.name for badge in user.badges])
            embed.add_field(
                name=f'{self.bot.emotes.staff_badge} Badges',
                value=badge_text,
                inline=False,
            )

    @commands.command(name='blacklist')
    @is_interchat_staff_check()
    async def blacklist_command(
        self,
        ctx: commands.Context,
        reason: str,
        duration: Optional[str] = None,
        # autocompleted to: {"id": "user's id", "name": "user's name"}
        user: Optional[str] = None,
        # autocompleted to: {"id": "server's id", "name": "server's name"}
        server: Optional[str] = None,
    ) -> None:
        """Blacklist a user or server from InterChat."""
        if user and server:
            await ctx.send('Please specify either a user or a server, not both.')
            return
        if not user and not server:
            await ctx.send('Please specify a user or a server to blacklist.')
            return

        if user:
            user_obj = json.loads(user)
            user_id = user_obj.get('id')
            user_name = user_obj.get('name')

            await blacklist_user(
                ctx.bot.db,
                user_id,
                str(ctx.author.id),
                reason,
                parse_duration(duration),
            )

            await ctx.send(
                f'{self.bot.emotes.blobFastBan} Blacklisted user **{user_name}** for reason: {reason}'
            )
        elif server:
            server_obj = json.loads(server)
            server_id = server_obj.get('id')
            server_name = server_obj.get('name')

            await blacklist_server(
                ctx.bot.db,
                server_id,
                str(ctx.author.id),
                reason,
                parse_duration(duration),
            )

            await ctx.send(
                f'{self.bot.emotes.blobFastBan} Blacklisted server **{server_name}** for reason: {reason}'
            )

    @commands.command(name='unblacklist')
    @is_interchat_staff_check()
    async def unblacklist_command(
        self,
        ctx: commands.Context,
        # below is autocompleted to: {"id": "user's id", "name": "user's name"}
        user: Optional[str] = None,
        # below is autocompleted to: {"id": "server's id", "name": "server's name"}
        server: Optional[str] = None,
    ) -> None:
        """Unblacklist a user or server from InterChat."""
        if user and server:
            await ctx.send('Please specify either a user or a server, not both.')
            return
        if not user and not server:
            await ctx.send('Please specify a user or a server to unblacklist.')
            return

        if user:
            user_obj = json.loads(user)
            user_id = user_obj.get('id')
            user_name = user_obj.get('name')

            await unblacklist_user(self.bot.db, user_id)
            await ctx.send(f'{self.bot.emotes.tick} Unblacklisted user **{user_name}**.')
        elif server:
            server_obj = json.loads(server)
            server_id = server_obj.get('id')
            server_name = server_obj.get('name')

            await unblacklist_server(self.bot.db, server_id)
            await ctx.send(f'{self.bot.emotes.tick} Unblacklisted server **{server_name}**.')


async def setup(bot: 'Bot') -> None:
    """Set up the Staff cog."""
    await bot.add_cog(Staff(bot))
