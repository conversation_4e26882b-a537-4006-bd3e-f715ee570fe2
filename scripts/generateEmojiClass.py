import asyncio
import discord
import os
import aiofiles

TOKEN = os.environ.get('TOKEN')


class EmojiDumpBot(discord.Client):
    async def on_ready(self):
        emojis = await self.fetch_application_emojis()
        print(f'Logged in as {self.user}')
        async with aiofiles.open(
            'utils/modules/emojis/emojis_static.py', 'w', encoding='utf-8'
        ) as f:
            print('Generating emojis_static.py...')
            await f.write('import discord\n\n')
            await f.write('class Emojis:\n')
            for emoji in emojis:
                safe_name = emoji.name.replace(' ', '_').replace('-', '_')
                await f.write(f'    {safe_name}: discord.Emoji\n')

        print(f'Generated {len(emojis)} emojis in emojis_static.py')
        await self.close()


async def main():
    intents = discord.Intents(guilds=True)
    async with EmojiDumpBot(intents=intents) as bot:
        if not TOKEN:
            raise ValueError('TOKEN environment variable not set')
        await bot.start(TOKEN)


asyncio.run(main())
