import argparse
import aiohttp
import discord
import os

from utils.modules.emojis.emojiLinks import EmojiLinks

TOKEN = os.environ['TOKEN']

intents = discord.Intents.none()
bot = discord.Client(intents=intents)


async def sync_all(force: bool):
    # fetch existing
    existing = await bot.fetch_application_emojis()
    existing_map = {e.name: e for e in existing}

    to_process = [
        (name, getattr(EmojiLinks, name)) for name in dir(EmojiLinks) if not name.startswith('__')
    ]

    async with aiohttp.ClientSession() as session:
        for name, url in to_process:
            if not force and name in existing_map:
                print(f'SKIP {name}')
                continue

            # if force & exists: delete old
            if name in existing_map:
                await existing_map[name].delete()
                print(f'DEL  {name}')

            # download & create
            async with session.get(url) as resp:
                if resp.status != 200:
                    print(f'ERR  {name} – HTTP {resp.status}')
                    continue
                img = await resp.read()
            emoji = await bot.create_application_emoji(name=name, image=img)
            print(f'CRT  {name} -> {emoji.id}')


async def main():
    await bot.login(TOKEN)
    # parse args
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--force', action='store_true', help='delete+recreate all emojis even if they exist'
    )
    args = parser.parse_args()
    await sync_all(force=args.force)
    await bot.close()


if __name__ == '__main__':
    import asyncio

    asyncio.run(main())
